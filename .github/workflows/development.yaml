name: Build and publish FE container

on:
  push:
    branches:
      - 'dev'
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'Tag for this FE image build'
        required: true
        default: 'development'

jobs:
  build-frontend:
    runs-on: ubuntu-latest
    env:
      PILLAR: development
    steps:
      - name: Checkout code repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }}
          password: ${{ secrets.DOCKER_SECRET }}

      - name: Build and push the FE image
        uses: docker/build-push-action@v4
        with:
          context: '.'
          push: true
          tags: ghcr.io/encorp-io/cip-dashboard:development
          build-args: |
          PROFILE=${{ env.PILLAR }}
          NEXT_PUBLIC_API_URL=https://staging.api.example.com
          NEXT_PUBLIC_ASSET_URL=https://staging.assets.example.com
