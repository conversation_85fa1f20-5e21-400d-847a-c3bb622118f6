name: Create and publish a container with 'latest' tag

on:
  push:
    branches:
      - 'main'
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'tag for this image build'
        required: true
        default: 'staging'

jobs:
  build-staging:
    runs-on: ubuntu-latest
    env:
      PILLAR: staging
    steps:
      - name: Checkout code repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Ensure full commit history
          token: ${{ secrets.DOCKER_SECRET }} # Set the access token

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2 # Use the updated version
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }} # Replace with your secret
          password: ${{ secrets.DOCKER_SECRET }} # Replace with your secret

      - name: Build and push the image
        uses: docker/build-push-action@v4
        with:
          context: '.'
          push: true
          tags: ghcr.io/encorp-io/cip-dashboard:staging
          build-args: PROFILE=${{ env.PILLAR }}
