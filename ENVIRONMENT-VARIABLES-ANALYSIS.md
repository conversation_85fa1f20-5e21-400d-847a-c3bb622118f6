# 🌍 Environment Variables Analysis - Client vs Server

## Executive Summary

This document analyzes which environment variables from your `.env.example` need the `NEXT_PUBLIC_` prefix for client-side access and which should remain server-side only for security.

---

## 🔍 Analysis Results

### ✅ **Variables That NEED `NEXT_PUBLIC_` Prefix (Client-Side)**

These variables are used in React components that run in the browser:

| Variable | Current Name | Required Name | Usage Location | Reason |
|----------|--------------|---------------|----------------|---------|
| `NEXT_PUBLIC_ASSET_URL` | ✅ Already correct | `NEXT_PUBLIC_ASSET_URL` | Client components | Image/asset URLs |
| `NEXT_PUBLIC_API_URL` | ✅ Already correct | `NEXT_PUBLIC_API_URL` | `axiosInstance.js` | API base URL |
| `DEFAULT_COMPANY_ID` | ❌ Missing prefix | `NEXT_PUBLIC_DEFAULT_COMPANY_ID` | Client components | Company filtering |
| `PHYSICAL_CARD_LIMIT` | ❌ Missing prefix | `NEXT_PUBLIC_PHYSICAL_CARD_LIMIT` | Client components | Card limits |
| `VIRTUAL_CARD_LIMIT` | ❌ Missing prefix | `NEXT_PUBLIC_VIRTUAL_CARD_LIMIT` | Client components | Card limits |

### 🔒 **Variables That MUST Stay Server-Side Only (No `NEXT_PUBLIC_`)**

These variables contain sensitive information and should NEVER be exposed to the client:

| Variable | Current Name | Keep As | Usage Location | Reason |
|----------|--------------|---------|----------------|---------|
| `DATABASE_URL` | ✅ Correct | `DATABASE_URL` | Server-side only | Contains DB credentials |
| `DB_NAME` | ✅ Correct | `DB_NAME` | Server-side only | Database configuration |
| `JWT_SECRET` | ✅ Correct | `JWT_SECRET` | Server-side only | Authentication secret |
| `EMAIL_USER` | ✅ Correct | `EMAIL_USER` | Server-side only | Email credentials |
| `EMAIL_PASS` | ✅ Correct | `EMAIL_PASS` | Server-side only | Email credentials |
| `NODE_ENV` | ✅ Correct | `NODE_ENV` | Server-side only | Environment config |
| `PORT` | ✅ Correct | `PORT` | Server-side only | Server configuration |
| `BACKEND_PORT` | ✅ Correct | `BACKEND_PORT` | Server-side only | Server configuration |

### ❓ **Variables Not Currently Used (Can Remove)**

| Variable | Status | Recommendation |
|----------|--------|----------------|
| `REACT_EDITOR` | Not found in codebase | Remove from `.env.example` |
| `API_URL` | Duplicate of `NEXT_PUBLIC_API_URL` | Remove from `.env.example` |

---

## 📋 **Updated `.env.example` File**

Here's how your `.env.example` should look:

```env
# 🌍 Ryvyl UI Environment Variables
# Copy this file and update with your actual values

# Core Application (Server-side only)
NODE_ENV=production/development/staging
BACKEND_PORT=3000
PORT=22344

# Database (Server-side only - NEVER expose these)
DATABASE_URL=your-database-url
DB_NAME=your-database-name

# Authentication & Security (Server-side only - CRITICAL)
JWT_SECRET=your-secret-key

# Email credentials (Server-side only)
EMAIL_USER=your-email
EMAIL_PASS=your-email-password

# API & Assets (Client-side - exposed to browser)
NEXT_PUBLIC_ASSET_URL=cip-backend
NEXT_PUBLIC_API_URL=cip-backend

# Default Values (Client-side - exposed to browser)
NEXT_PUBLIC_DEFAULT_COMPANY_ID=your-default-company-id
NEXT_PUBLIC_PHYSICAL_CARD_LIMIT=5 
NEXT_PUBLIC_VIRTUAL_CARD_LIMIT=6
```

---

## 🔧 **Required Code Changes**

### 1. Update Client-Side Component Usage

**File**: `src/app/corporate/cards/page.tsx` (Line 21-23)
```typescript
// ❌ Current (will be undefined in production)
const DEFAULT_COMPANY_ID = process.env.DEFAULT_COMPANY_ID;
const PHYSICAL_CARD_LIMIT = process.env.PHYSICAL_CARD_LIMIT;
const VIRTUAL_CARD_LIMIT = process.env.VIRTUAL_CARD_LIMIT;

// ✅ Fixed (will work in production)
const DEFAULT_COMPANY_ID = process.env.NEXT_PUBLIC_DEFAULT_COMPANY_ID;
const PHYSICAL_CARD_LIMIT = process.env.NEXT_PUBLIC_PHYSICAL_CARD_LIMIT;
const VIRTUAL_CARD_LIMIT = process.env.NEXT_PUBLIC_VIRTUAL_CARD_LIMIT;
```

**File**: `src/app/cardholder/cards/page.tsx` (Line 20-22)
```typescript
// ❌ Current
const DEFAULT_COMPANY_ID = process.env.DEFAULT_COMPANY_ID;
const PHYSICAL_CARD_LIMIT = Number(process.env.PHYSICAL_CARD_LIMIT);
const VIRTUAL_CARD_LIMIT = Number(process.env.VIRTUAL_CARD_LIMIT);

// ✅ Fixed
const DEFAULT_COMPANY_ID = process.env.NEXT_PUBLIC_DEFAULT_COMPANY_ID;
const PHYSICAL_CARD_LIMIT = Number(process.env.NEXT_PUBLIC_PHYSICAL_CARD_LIMIT);
const VIRTUAL_CARD_LIMIT = Number(process.env.NEXT_PUBLIC_VIRTUAL_CARD_LIMIT);
```

**File**: `src/app/lite/admin/inventory/[company]/products/BIN-view/[cip]/bin-allocation/page.tsx` (Line 109)
```typescript
// ❌ Current
const companyImages = imagesResponse.data.filter((image: any) => image.company._id === process.env.DEFAULT_COMPANY_ID)

// ✅ Fixed
const companyImages = imagesResponse.data.filter((image: any) => image.company._id === process.env.NEXT_PUBLIC_DEFAULT_COMPANY_ID)
```

### 2. Update Dockerfile Build Args

**File**: `Dockerfile` (Line 13-21)
```dockerfile
# ✅ Current is mostly correct, just add NEXT_PUBLIC_ prefix
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_ASSET_URL
ARG NEXT_PUBLIC_DEFAULT_COMPANY_ID
ARG NEXT_PUBLIC_PHYSICAL_CARD_LIMIT
ARG NEXT_PUBLIC_VIRTUAL_CARD_LIMIT

ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_ASSET_URL=$NEXT_PUBLIC_ASSET_URL
ENV NEXT_PUBLIC_DEFAULT_COMPANY_ID=$NEXT_PUBLIC_DEFAULT_COMPANY_ID
ENV NEXT_PUBLIC_PHYSICAL_CARD_LIMIT=$NEXT_PUBLIC_PHYSICAL_CARD_LIMIT
ENV NEXT_PUBLIC_VIRTUAL_CARD_LIMIT=$NEXT_PUBLIC_VIRTUAL_CARD_LIMIT
```

---

## 🚨 **Security Implications**

### ⚠️ **CRITICAL: Variables That Must NEVER Have `NEXT_PUBLIC_`**

```env
# ❌ NEVER DO THIS - Exposes secrets to browser
NEXT_PUBLIC_DATABASE_URL=***************************  # 🚨 SECURITY BREACH
NEXT_PUBLIC_JWT_SECRET=your-secret-key                # 🚨 SECURITY BREACH
NEXT_PUBLIC_EMAIL_PASS=your-password                  # 🚨 SECURITY BREACH
```

### ✅ **Safe to Expose (Already Public Information)**

```env
# ✅ Safe - These are already visible to users
NEXT_PUBLIC_API_URL=https://yourdomain.com
NEXT_PUBLIC_ASSET_URL=https://assets.yourdomain.com
NEXT_PUBLIC_DEFAULT_COMPANY_ID=company123
NEXT_PUBLIC_PHYSICAL_CARD_LIMIT=5
NEXT_PUBLIC_VIRTUAL_CARD_LIMIT=6
```

---

## 🎯 **Docker Build Command Example**

When building your Docker image, pass the client-side variables as build args:

```bash
docker build \
  --build-arg NEXT_PUBLIC_API_URL=https://yourdomain.com \
  --build-arg NEXT_PUBLIC_ASSET_URL=https://assets.yourdomain.com \
  --build-arg NEXT_PUBLIC_DEFAULT_COMPANY_ID=your-company-id \
  --build-arg NEXT_PUBLIC_PHYSICAL_CARD_LIMIT=5 \
  --build-arg NEXT_PUBLIC_VIRTUAL_CARD_LIMIT=6 \
  -t ryvyl-cards .
```

---

## 📝 **Summary of Changes Needed**

### 1. **Environment Variables** (Update your `.env` file):
- ✅ Keep: `DATABASE_URL`, `JWT_SECRET`, `EMAIL_*` (server-side)
- 🔄 Rename: `DEFAULT_COMPANY_ID` → `NEXT_PUBLIC_DEFAULT_COMPANY_ID`
- 🔄 Rename: `PHYSICAL_CARD_LIMIT` → `NEXT_PUBLIC_PHYSICAL_CARD_LIMIT`
- 🔄 Rename: `VIRTUAL_CARD_LIMIT` → `NEXT_PUBLIC_VIRTUAL_CARD_LIMIT`

### 2. **Code Changes** (Update 3 files):
- `src/app/corporate/cards/page.tsx`
- `src/app/cardholder/cards/page.tsx`
- `src/app/lite/admin/inventory/[company]/products/BIN-view/[cip]/bin-allocation/page.tsx`

### 3. **Docker Configuration**:
- Update Dockerfile build args with `NEXT_PUBLIC_` prefix
- Update your Portainer environment variables

---

## ✅ **Verification Checklist**

After making these changes:

- [ ] Updated `.env` file with `NEXT_PUBLIC_` prefixes
- [ ] Updated all client-side component references
- [ ] Updated Dockerfile build args
- [ ] Updated Portainer environment variables
- [ ] Tested that client-side variables are accessible
- [ ] Verified server-side variables remain secure
- [ ] Confirmed application builds successfully
- [ ] Tested application functionality in production

This analysis ensures your environment variables are properly configured for both security and functionality in your Docker deployment!
