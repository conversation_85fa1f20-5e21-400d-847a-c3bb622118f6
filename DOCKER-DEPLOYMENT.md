# Docker Deployment Guide for Ryvyl Cards

## Overview

This guide explains how to deploy the Ryvyl Cards Next.js application using Docker and Docker Compose. The setup includes the main application, MongoDB database, Redis cache, and optional Nginx reverse proxy.

## Port Configuration Explained

### Default Port Setup
- **Application**: Port 3000 (internal and external)
- **MongoDB**: Port 27017 (external), 27017 (internal)
- **Redis**: Port 6379 (external), 6379 (internal)
- **Nginx**: Port 80 (HTTP), 443 (HTTPS)

### Port Mapping in Docker Compose
```yaml
services:
  ryvyl-app:
    ports:
      - "3000:3000"  # host:container
    # This means: localhost:3000 -> container:3000
  
  mongodb:
    ports:
      - "27017:27017"  # host:container
    # This means: localhost:27017 -> container:27017
```

## Quick Start

### 1. Prerequisites
```bash
# Install Docker and Docker Compose
# On Ubuntu/Debian:
sudo apt update
sudo apt install docker.io docker-compose

# On macOS (using Homebrew):
brew install docker docker-compose

# On Windows: Install Docker Desktop
```

### 2. Environment Setup
```bash
# Copy the environment template
cp .env.docker .env

# Edit the environment file with your values
nano .env
```

**Important Environment Variables to Update:**
```env
# CRITICAL: Change these in production
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long-change-this-in-production
MONGO_ROOT_PASSWORD=your-secure-mongodb-password

# Update with your domain
NEXT_PUBLIC_API_URL=https://yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Email configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### 3. Build and Run
```bash
# Build and start all services
docker-compose up -d

# Check if services are running
docker-compose ps

# View logs
docker-compose logs -f ryvyl-app
```

### 4. Verify Deployment
```bash
# Check application health
curl http://localhost:3000

# Check MongoDB connection
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')"

# Check Redis connection
docker-compose exec redis redis-cli ping
```

## Detailed Configuration

### Application Container
The main application runs on port 3000 inside the container and is exposed to the host on the same port.

**Key Configuration:**
- **Base Image**: Node.js 18 Alpine
- **Working Directory**: `/app`
- **User**: Non-root user (nextjs:nodejs)
- **Environment**: Production mode
- **Health Check**: Built-in Next.js health endpoint

### Database Container (MongoDB)
MongoDB runs on the standard port 27017 with authentication enabled.

**Key Configuration:**
- **Image**: MongoDB 6.0
- **Authentication**: Root user with password
- **Database**: `ryvyl-beta`
- **Persistence**: Docker volume `mongodb_data`
- **Initialization**: Custom script creates indexes

### Cache Container (Redis)
Redis provides caching capabilities for improved performance.

**Key Configuration:**
- **Image**: Redis 7 Alpine
- **Port**: 6379
- **Persistence**: Docker volume `redis_data`
- **Configuration**: Default Redis configuration

### Reverse Proxy (Nginx) - Optional
Nginx provides SSL termination, rate limiting, and load balancing.

**Key Configuration:**
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Rate Limiting**: API endpoints protected
- **SSL**: Ready for certificate configuration
- **Security Headers**: Comprehensive security headers

## Production Deployment

### 1. Security Hardening
```bash
# Generate a strong JWT secret
openssl rand -base64 32

# Create secure MongoDB password
openssl rand -base64 24

# Update environment variables
nano .env
```

### 2. SSL Certificate Setup
```bash
# Create SSL directory
mkdir -p ssl

# Copy your SSL certificates
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem

# Update nginx.conf to enable HTTPS
# Uncomment the HTTPS server block in nginx.conf
```

### 3. Production Environment Variables
```env
NODE_ENV=production
COOKIE_SECURE=true
ALLOWED_ORIGINS=https://yourdomain.com
NEXT_PUBLIC_API_URL=https://yourdomain.com
```

### 4. Resource Limits
Add resource limits to docker-compose.yml:
```yaml
services:
  ryvyl-app:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
sudo lsof -i :3000

# Kill the process or change the port in docker-compose.yml
ports:
  - "3001:3000"  # Use port 3001 instead
```

#### 2. Database Connection Issues
```bash
# Check MongoDB logs
docker-compose logs mongodb

# Test connection
docker-compose exec ryvyl-app node -e "
const { MongoClient } = require('mongodb');
const client = new MongoClient(process.env.DATABASE_URL);
client.connect().then(() => console.log('Connected')).catch(console.error);
"
```

#### 3. Application Won't Start
```bash
# Check application logs
docker-compose logs ryvyl-app

# Check environment variables
docker-compose exec ryvyl-app env | grep -E "(JWT_SECRET|DATABASE_URL)"

# Rebuild the container
docker-compose down
docker-compose build --no-cache ryvyl-app
docker-compose up -d
```

#### 4. File Upload Issues
```bash
# Check upload directory permissions
docker-compose exec ryvyl-app ls -la /app/uploads

# Create uploads directory if needed
docker-compose exec ryvyl-app mkdir -p /app/uploads
docker-compose exec ryvyl-app chown nextjs:nodejs /app/uploads
```

### Useful Commands

```bash
# View all containers
docker-compose ps

# View logs for specific service
docker-compose logs -f mongodb

# Execute command in container
docker-compose exec ryvyl-app bash

# Restart specific service
docker-compose restart ryvyl-app

# Update and restart
docker-compose pull
docker-compose up -d

# Clean up
docker-compose down -v  # Removes volumes too
docker system prune -a  # Clean up unused images
```

## Monitoring and Maintenance

### Health Checks
```bash
# Application health
curl http://localhost:3000/api/health

# Database health
docker-compose exec mongodb mongosh --eval "db.stats()"

# Redis health
docker-compose exec redis redis-cli info server
```

### Backup
```bash
# Backup MongoDB
docker-compose exec mongodb mongodump --out /backup
docker cp $(docker-compose ps -q mongodb):/backup ./mongodb-backup

# Backup Redis
docker-compose exec redis redis-cli BGSAVE
```

### Log Management
```bash
# Rotate logs
docker-compose logs --tail=1000 ryvyl-app > app.log

# Clear logs
docker-compose down
docker system prune -f
docker-compose up -d
```

## Scaling

### Horizontal Scaling
```yaml
# In docker-compose.yml
services:
  ryvyl-app:
    deploy:
      replicas: 3
    ports:
      - "3000-3002:3000"
```

### Load Balancing with Nginx
Update nginx.conf:
```nginx
upstream ryvyl_app {
    server ryvyl-app-1:3000;
    server ryvyl-app-2:3000;
    server ryvyl-app-3:3000;
}
```

## Security Checklist

- [ ] JWT_SECRET is strong and unique
- [ ] MongoDB has authentication enabled
- [ ] HTTPS is configured with valid certificates
- [ ] Rate limiting is enabled
- [ ] Security headers are configured
- [ ] File upload limits are set
- [ ] Environment variables are secured
- [ ] Container runs as non-root user
- [ ] Unnecessary ports are not exposed
- [ ] Regular security updates are applied

## Performance Optimization

### 1. Enable Redis Caching
```javascript
// In your application
const redis = require('redis');
const client = redis.createClient(process.env.REDIS_URL);
```

### 2. Database Optimization
```javascript
// Ensure indexes are created (handled by mongo-init.js)
// Monitor slow queries
db.setProfilingLevel(2, { slowms: 100 });
```

### 3. Container Optimization
```dockerfile
# Multi-stage build reduces image size
# Use .dockerignore to exclude unnecessary files
# Use Alpine images for smaller footprint
```

This setup provides a robust, scalable, and secure deployment for your Ryvyl Cards application!
