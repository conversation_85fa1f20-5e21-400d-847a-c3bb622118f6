# 🌍 Ryvyl UI Environment Variables
# Copy this file and update with your actual values

# Core Application
REACT_EDITOR=<your-editor>
NODE_ENV=production/development/staging
BACKEND_PORT=3000
PORT=22344

# Database
DATABASE_URL=your-database-url
DB_NAME=your-database-name

# Authentication & Security
JWT_SECRET=your-secret-key

# Email credentials
EMAIL_USER=your-email
EMAIL_PASS=your-email-password

# API & Assets
NEXT_PUBLIC_ASSET_URL=cip-backend
NEXT_PUBLIC_API_URL=cip-backend
API_URL=cip-backend

# Default Values
NEXT_PUBLIC_DEFAULT_COMPANY_ID=your-default-company-id
NEXT_PUBLIC_PHYSICAL_CARD_LIMIT=5 
NEXT_PUBLIC_VIRTUAL_CARD_LIMIT=6