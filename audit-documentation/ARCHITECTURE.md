# Architecture Analysis - Ryvyl Cards

## Executive Summary

Ryvyl Cards implements a **hybrid monolithic architecture** with Next.js App Router, featuring a multi-tenant card management platform. The architecture combines server-side rendering, API routes, and a custom Express server, supporting multiple dashboard types with role-based access control.

## Architectural Pattern

### Primary Pattern: **Modular Monolith with Multi-Tenant Design**

The application follows a modular monolithic pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Next.js Application                      │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React + TypeScript)                             │
│  ├── Multi-Dashboard UI (Cardholder, Corporate, Manager)   │
│  ├── Shared Components (Radix UI + Tailwind)              │
│  └── State Management (Redux Toolkit)                      │
├─────────────────────────────────────────────────────────────┤
│  API Layer (Next.js API Routes)                           │
│  ├── Data Processing APIs                                  │
│  ├── File Upload/Export APIs                              │
│  └── Activity Tracking APIs                               │
├─────────────────────────────────────────────────────────────┤
│  Custom Express Server                                     │
│  ├── Authentication Routes                                 │
│  ├── User Management                                       │
│  └── Role/Company Management                               │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                │
│  ├── MongoDB (Primary Database)                           │
│  ├── Mongoose ODM                                         │
│  └── Activity Logging                                     │
└─────────────────────────────────────────────────────────────┘
```

## Main Application Flows

### 1. Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Express
    participant MongoDB
    participant NextJS

    User->>Frontend: Login Request
    Frontend->>Express: POST /api/users/login
    Express->>MongoDB: Validate Credentials
    MongoDB-->>Express: User Data + Roles
    Express-->>Frontend: JWT Token + User Info
    Frontend->>Frontend: Store Token in Cookie
    Frontend->>NextJS: Navigate to Dashboard
    NextJS->>NextJS: Middleware Validates Token
    NextJS-->>Frontend: Render Dashboard
```

### 2. Data Processing Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant MongoDB
    participant FileSystem

    User->>Frontend: Upload File
    Frontend->>API: POST /api/save-records
    API->>API: Validate Records
    API->>MongoDB: Save File Metadata
    API->>MongoDB: Bulk Insert Records
    MongoDB-->>API: Confirmation
    API-->>Frontend: Success Response
    Frontend->>Frontend: Update UI
```

### 3. Multi-Tenant Access Flow

```mermaid
flowchart TD
    A[User Login] --> B{Dashboard Type?}
    B -->|cardholder| C[Cardholder Layout]
    B -->|corporate| D[Corporate Layout]
    B -->|programmeManager| E[Manager Layout]
    B -->|admin| F[Lite Admin Layout]
    
    C --> G[Check: dashboard === 'cardholder' && recordId !== null]
    D --> H[Check: dashboard === 'corporate' && recordId !== null]
    E --> I[Check: dashboard === 'programmeManager' && recordId !== null]
    F --> J[Check: dashboard !== 'cardholder' && recordId == null]
    
    G --> K[Load Cardholder Permissions]
    H --> L[Load Corporate Permissions]
    I --> M[Load Manager Permissions]
    J --> N[Load Admin Permissions]
```

## Routing Architecture

### Next.js App Router Structure

```
src/app/
├── (auth)/
│   ├── login/
│   ├── forget-password/
│   └── onboarding/
├── cardholder/          # Individual cardholder interface
│   ├── layout.tsx       # Cardholder-specific layout
│   ├── dashboard/
│   ├── cards/
│   └── transactions/
├── corporate/           # Corporate management interface
│   ├── layout.tsx       # Corporate-specific layout
│   ├── dashboard/
│   ├── employees/
│   └── reports/
├── manager/             # Programme manager interface
│   ├── layout.tsx       # Manager-specific layout
│   ├── dashboard/
│   └── analytics/
├── lite/                # Lightweight admin interface
│   ├── admin/
│   │   ├── dashboard/
│   │   ├── users/
│   │   └── settings/
│   └── layout.tsx
├── b2b/                 # B2B partner interface
├── individual/          # Individual user interface
└── api/                 # API routes
    ├── activity/
    ├── save-records/
    ├── get-records/
    └── [other-endpoints]/
```

### Route Protection Strategy

Each dashboard implements its own authentication logic in `layout.tsx`:

```typescript
// Pattern used across all dashboard layouts
useEffect(() => {
    const checkAuth = async () => {
        try {
            const response = await axiosInstance.get("users/me", { withCredentials: true })
            const user = response?.data
            
            // Dashboard-specific access control
            if (user && user.dashboard === "cardholder" && user.recordId !== null) {
                // Load permissions and set authenticated state
            } else {
                router.push("/login")
            }
        } catch (error) {
            router.push("/login")
        }
    }
    checkAuth()
}, [router, dispatch])
```

## Data Fetching Patterns

### 1. Client-Side Data Fetching (Primary Pattern)

```typescript
// Typical data fetching pattern
const [data, setData] = useState([])
const [loading, setLoading] = useState(true)

useEffect(() => {
    const fetchData = async () => {
        try {
            const response = await axiosInstance.get('/endpoint')
            setData(response.data)
        } catch (error) {
            console.error('Error fetching data:', error)
        } finally {
            setLoading(false)
        }
    }
    fetchData()
}, [])
```

### 2. Server Actions (Limited Usage)

```typescript
// Used in settings for server-side operations
"use server"
async function getAuthToken() {
    const cookieStore = await cookies()
    return cookieStore.get("token")?.value
}
```

### 3. API Route Handlers

```typescript
// Next.js API routes for data operations
export async function POST(request: NextRequest) {
    const body = await request.json()
    // Process data
    return NextResponse.json({ success: true, data })
}
```

## SSR/SSG Usage Analysis

### Current Implementation: **Minimal SSR/SSG Usage**

1. **Client-Side Rendering (CSR) Dominant**
   - Most pages use `'use client'` directive
   - Data fetching happens after component mount
   - Authentication checks on client-side

2. **Limited Server-Side Features**
   - Server actions used sparingly
   - No static generation for public pages
   - No ISR (Incremental Static Regeneration)

3. **Hybrid Approach Opportunities Missed**
   - Could benefit from SSR for SEO
   - Static generation for public content
   - Server-side authentication checks

## Database Architecture

### MongoDB Schema Design

```javascript
// Activity tracking schema
const activitySchema = new mongoose.Schema({
    url: { type: String, required: true, trim: true },
    pathname: { type: String, required: true, trim: true, index: true },
    method: { type: String, required: true, enum: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"] },
    userAgent: { type: String, required: true },
    ip: { type: String, required: true, index: true },
    timestamp: { type: Date, required: true, index: true },
    userId: { type: mongoose.Schema.Types.ObjectId, ref: "User", index: true }
})

// Compound indexes for analytics
activitySchema.index({ timestamp: -1, pathname: 1 })
activitySchema.index({ ip: 1, timestamp: -1 })
```

### Data Processing Collections

```javascript
// Dynamic record structure for file uploads
export interface PocztaPostRecord {
    _id?: string
    [key: string]: any  // Dynamic fields from uploaded data
    _fileName: string
    _sheetName: string
    _rowIndex: number
    _totalRows: number
    _uploadedAt: Date
}
```

## Scalability Considerations

### Current Limitations

1. **Single Database Instance**
   - No database sharding
   - No read replicas
   - Potential bottleneck for large datasets

2. **Monolithic Deployment**
   - Single application instance
   - No horizontal scaling strategy
   - Resource contention between features

3. **Client-Side Heavy Operations**
   - Large data processing on client
   - Memory-intensive operations
   - Poor performance on low-end devices

### Performance Bottlenecks

1. **Activity Logging Overhead**
   - Every request triggers database write
   - No batching or queuing
   - Potential performance impact

2. **Large File Processing**
   - Synchronous file processing
   - No background job processing
   - Memory limitations

3. **Inefficient Data Fetching**
   - No caching strategy
   - Repeated API calls
   - Large payload transfers

## Improvement Plan

### Phase 1: Immediate Optimizations (1-2 weeks)

1. **Implement Caching**
   ```typescript
   // Add Redis for session and data caching
   import Redis from 'ioredis'
   const redis = new Redis(process.env.REDIS_URL)
   ```

2. **Optimize Database Queries**
   - Add missing indexes
   - Implement query optimization
   - Add connection pooling

3. **Implement Rate Limiting**
   ```typescript
   import rateLimit from 'express-rate-limit'
   const limiter = rateLimit({
       windowMs: 15 * 60 * 1000, // 15 minutes
       max: 100 // limit each IP to 100 requests per windowMs
   })
   ```

### Phase 2: Architectural Improvements (1-2 months)

1. **Microservices Extraction**
   - Authentication service
   - File processing service
   - Activity logging service

2. **Background Job Processing**
   ```typescript
   // Implement job queue for heavy operations
   import Bull from 'bull'
   const fileProcessingQueue = new Bull('file processing')
   ```

3. **Enhanced SSR/SSG**
   - Server-side authentication
   - Static generation for public pages
   - Improved SEO and performance

### Phase 3: Scalability Enhancements (3-6 months)

1. **Database Scaling**
   - Read replicas
   - Sharding strategy
   - Data archiving

2. **CDN and Edge Computing**
   - Static asset optimization
   - Edge caching
   - Global distribution

3. **Monitoring and Observability**
   - Application performance monitoring
   - Distributed tracing
   - Real-time alerting

## Technology Stack Assessment

### Strengths
- Modern React/Next.js foundation
- TypeScript for type safety
- Comprehensive UI component library
- Flexible database schema

### Weaknesses
- Limited use of Next.js SSR capabilities
- No caching strategy
- Monolithic architecture constraints
- Performance optimization gaps

### Recommendations
- Leverage Next.js SSR/SSG features
- Implement comprehensive caching
- Consider microservices for specific domains
- Add performance monitoring and optimization

---

**Architecture Maturity Level**: **Intermediate**
**Scalability Rating**: **Limited** (requires significant improvements)
**Maintainability**: **Good** (with proper refactoring)
