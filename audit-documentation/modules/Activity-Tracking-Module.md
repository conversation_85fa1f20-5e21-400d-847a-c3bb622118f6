# Activity Tracking Module Documentation

## Module Identity

- **Module Name**: Activity Tracking & Analytics System
- **Type**: Monitoring & Analytics Module
- **Repository**: Ryvyl Cards Monorepo
- **Framework**: Next.js 14 Middleware + MongoDB
- **Version**: 1.0.0
- **Last Updated**: 2024-12-09

## Purpose and Functionality

### Business Purpose
The Activity Tracking module provides comprehensive user activity monitoring, analytics, and audit trails for the Ryvyl Cards platform. It captures detailed user interactions, system events, and performance metrics for security, compliance, and business intelligence purposes.

### Core Features
1. **Real-time Activity Logging**
   - Page visit tracking
   - User interaction monitoring
   - Request/response logging
   - Performance metrics collection

2. **User Analytics**
   - Session tracking
   - Geographic analysis
   - Device fingerprinting
   - Behavior patterns

3. **Security Monitoring**
   - Authentication events
   - Failed access attempts
   - Suspicious activity detection
   - IP-based tracking

4. **Performance Monitoring**
   - Response time tracking
   - Error rate monitoring
   - System health metrics
   - Resource usage analytics

### Target Users
- **Security Teams**: Monitoring for threats and compliance
- **Product Managers**: Understanding user behavior
- **System Administrators**: Performance and health monitoring
- **Business Analysts**: Usage analytics and reporting

## Technical Architecture

### Architectural Pattern
**Event-Driven Monitoring** with middleware-based data collection

### Key Components

#### 1. Next.js Middleware (`src/middleware.ts`)
```typescript
export async function middleware(request: NextRequest) {
    const response = NextResponse.next()
    
    if (shouldLogActivity(request)) {
        const activityData = {
            url: request.url,
            pathname: request.nextUrl.pathname,
            method: request.method,
            userAgent: request.headers.get("user-agent") || "",
            ip: extractIP(request),
            user: extractUserInfo(request),
            geo: extractGeoInfo(request),
            device: extractDeviceInfo(request),
            timestamp: new Date().toISOString()
        }
        
        // Non-blocking activity logging
        fetch(`${request.nextUrl.origin}/api/activity`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(activityData)
        }).catch(() => {})
    }
    
    return response
}
```

#### 2. Activity Model (`src/models/Activity.js`)
```javascript
const activitySchema = new mongoose.Schema({
    url: { type: String, required: true, trim: true },
    pathname: { type: String, required: true, trim: true, index: true },
    method: { type: String, required: true, enum: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"] },
    userAgent: { type: String, required: true },
    ip: { type: String, required: true, index: true },
    timestamp: { type: Date, required: true, index: true },
    userId: { type: mongoose.Schema.Types.ObjectId, ref: "User", index: true },
    sessionId: { type: String, index: true },
    deviceType: { type: String, enum: ["mobile", "desktop", "tablet", "unknown"] },
    responseTime: { type: Number, min: 0 },
    statusCode: { type: Number, min: 100, max: 599 }
}, {
    timestamps: true,
    collection: "activities"
})
```

#### 3. Activity API (`src/app/api/activity/route.ts`)
```typescript
export async function POST(request: NextRequest) {
    try {
        const activityData = await request.json()
        
        // Extract user ID from JWT if available
        const userId = await getCurrentUserId(request)
        if (userId) {
            activityData.userId = userId
        }
        
        // Save to database
        const activity = new Activity(activityData)
        await activity.save()
        
        return NextResponse.json({ success: true })
    } catch (error) {
        console.error("Activity logging error:", error)
        return NextResponse.json({ error: "Failed to log activity" }, { status: 500 })
    }
}
```

### Data Flow
1. **Request Interception**: Middleware captures all requests
2. **Data Extraction**: User, device, and request information extracted
3. **Filtering**: Bot traffic and static assets filtered out
4. **Enrichment**: Geographic and device data added
5. **Storage**: Activity data saved to MongoDB
6. **Analytics**: Data aggregated for reporting

### Performance Characteristics
- **Logging Overhead**: ~2-5ms per request
- **Database Writes**: Asynchronous, non-blocking
- **Memory Usage**: Minimal (streaming data)
- **Storage Growth**: ~1KB per activity record

## Dependencies and Integrations

### External Libraries
```json
{
  "mongoose": "^8.17.2",       // Database ODM
  "mongodb": "^6.18.0"         // Database driver
}
```

### Internal Dependencies
- **Next.js Middleware**: Request interception
- **JWT Utilities**: User identification
- **MongoDB Connection**: Data persistence
- **Error Handling**: Centralized error management

### Database Collections
- **activities**: Main activity tracking collection
- **users**: User reference for activity correlation

## Development Info

### Build Process
Activity tracking is automatically included in the Next.js build process.

### Testing
**Status**: ❌ **No tests currently implemented**

**Required Test Coverage**:
- Unit tests for activity filtering logic
- Integration tests for middleware functionality
- Performance tests for high-volume logging
- Privacy compliance tests

### Code Quality Issues
1. **🟡 Medium**: No error boundaries for activity logging
2. **🟡 Medium**: Limited data validation
3. **🟢 Low**: Console logging for errors

## Deployment & Operations

### Configuration Management
```typescript
// Activity tracking configuration
const config = {
    enableTracking: process.env.ENABLE_ACTIVITY_TRACKING !== 'false',
    batchSize: parseInt(process.env.ACTIVITY_BATCH_SIZE || '100'),
    flushInterval: parseInt(process.env.ACTIVITY_FLUSH_INTERVAL || '5000'),
    retentionDays: parseInt(process.env.ACTIVITY_RETENTION_DAYS || '90')
}
```

### Scaling Considerations
- **High Volume**: Implement batching for database writes
- **Storage Growth**: Implement data retention policies
- **Query Performance**: Optimize indexes for analytics
- **Real-time Analytics**: Consider streaming analytics

### Environment Variables
```env
ENABLE_ACTIVITY_TRACKING=true
ACTIVITY_BATCH_SIZE=100
ACTIVITY_FLUSH_INTERVAL=5000
ACTIVITY_RETENTION_DAYS=90
```

## Monitoring & Health

### Current Logging
```typescript
console.error("Activity logging error:", error)
console.error("JWT decode error:", error)
```

### Recommended Monitoring
- **Logging Success Rate**: Track failed activity logs
- **Database Performance**: Monitor write performance
- **Data Quality**: Validate activity data integrity
- **Privacy Compliance**: Monitor data collection practices

### Analytics Capabilities
```javascript
// Built-in analytics methods
activitySchema.statics.getActivityStats = async function(startDate, endDate) {
    const pipeline = [
        {
            $match: {
                timestamp: { $gte: startDate, $lte: endDate }
            }
        },
        {
            $group: {
                _id: null,
                totalActivities: { $sum: 1 },
                uniqueIPs: { $addToSet: "$ip" },
                uniquePages: { $addToSet: "$pathname" }
            }
        }
    ]
    return this.aggregate(pipeline)
}
```

## Database Usage

### Schema Design
```javascript
{
    _id: ObjectId,
    url: String,                    // Full request URL
    pathname: String,               // Request path (indexed)
    method: String,                 // HTTP method
    userAgent: String,              // Browser/client info
    referer: String,               // Referring page
    ip: String,                    // Client IP (indexed)
    timestamp: Date,               // Request timestamp (indexed)
    userId: ObjectId,              // User reference (indexed)
    sessionId: String,             // Session identifier
    deviceType: String,            // Device classification
    browser: String,               // Browser type
    os: String,                    // Operating system
    country: String,               // Geographic location
    responseTime: Number,          // Response duration
    statusCode: Number,            // HTTP status code
    createdAt: Date,               // Record creation
    updatedAt: Date                // Record modification
}
```

### Indexes
```javascript
// Performance indexes
db.activities.createIndex({ timestamp: -1, pathname: 1 })
db.activities.createIndex({ ip: 1, timestamp: -1 })
db.activities.createIndex({ userId: 1, timestamp: -1 })
db.activities.createIndex({ method: 1, timestamp: -1 })
db.activities.createIndex({ deviceType: 1, timestamp: -1 })

// TTL index for data retention
db.activities.createIndex({ timestamp: 1 }, { expireAfterSeconds: 7776000 }) // 90 days
```

## Security Considerations

### Privacy Concerns
1. **🟡 Medium**: Extensive user tracking without explicit consent
2. **🟡 Medium**: IP address logging for extended periods
3. **🟡 Medium**: User agent fingerprinting
4. **🟡 Medium**: Geographic location tracking

### Data Protection
```typescript
// Recommended privacy enhancements
const sanitizeActivityData = (data) => {
    return {
        ...data,
        ip: hashIP(data.ip),                    // Hash IP addresses
        userAgent: sanitizeUserAgent(data.userAgent), // Remove identifying info
        // Remove or hash other PII
    }
}
```

### GDPR Compliance
**Required Implementations**:
- User consent management
- Data anonymization
- Right to deletion
- Data export capabilities

## APIs & Integration Points

### Activity Logging API
```typescript
// Log activity
POST /api/activity
Body: {
    url: string,
    pathname: string,
    method: string,
    userAgent: string,
    ip: string,
    timestamp: string,
    user?: object,
    geo?: object,
    device?: object
}
Response: { success: boolean }
```

### Analytics Queries
```typescript
// Get activity statistics
GET /api/analytics/activities?startDate=X&endDate=Y
Response: {
    totalActivities: number,
    uniqueUsers: number,
    topPages: Array<{pathname: string, count: number}>,
    deviceBreakdown: object
}
```

## Operational Procedures

### Data Retention
```javascript
// Automated cleanup job
const cleanupOldActivities = async () => {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - 90)
    
    await Activity.deleteMany({
        timestamp: { $lt: cutoffDate }
    })
}
```

### Performance Optimization
1. **Batch Processing**: Group database writes
2. **Index Maintenance**: Regular index optimization
3. **Data Archiving**: Move old data to cold storage
4. **Query Optimization**: Optimize analytics queries

## Development Context for AI Agents

### Extension Points
1. **Custom Events**: Add application-specific event tracking
2. **Real-time Analytics**: Implement streaming analytics
3. **Alerting**: Add anomaly detection and alerting
4. **Dashboards**: Create analytics dashboards

### Common Patterns
```typescript
// Activity filtering pattern
function shouldLogActivity(request: NextRequest): boolean {
    const { pathname } = request.nextUrl
    const userAgent = request.headers.get("user-agent") || ""
    
    // Skip bots
    if (/bot|crawler|spider/i.test(userAgent)) return false
    
    // Skip static assets
    if (pathname.startsWith("/_next/") || pathname.includes(".")) return false
    
    return true
}

// User identification pattern
const extractUserInfo = (request: NextRequest) => {
    const token = request.cookies.get("authToken")?.value
    if (token) {
        const decoded = decodeJWT(token)
        return {
            userId: decoded?.payload?.id,
            isAuthenticated: !decoded?.isExpired
        }
    }
    return { isAuthenticated: false }
}
```

### Impact of Changes
- **Schema Changes**: Require database migration
- **Filtering Changes**: Affect data collection volume
- **Privacy Changes**: Impact compliance requirements
- **Performance Changes**: Affect application responsiveness

## Ownership & Contact

### Responsible Team
- **Primary**: Platform Engineering Team
- **Secondary**: Data Engineering Team
- **Privacy**: Legal & Compliance Team

### Subject Matter Experts
- **Analytics**: Senior Data Engineer
- **Privacy**: Privacy Officer
- **Performance**: Platform Architect

---

**Module Status**: 🟡 **Functional with privacy concerns**
**Privacy Rating**: 🟡 **Medium Risk** (extensive tracking without consent)
**Performance Rating**: 🟢 **Good** (optimized for high volume)
**Compliance Priority**: 🟡 **Medium** (GDPR compliance needed)
