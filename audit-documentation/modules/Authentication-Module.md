# Authentication Module Documentation

## Module Identity

- **Module Name**: Authentication & Authorization System
- **Type**: Core Security Module
- **Repository**: Ryvyl Cards Monorepo
- **Framework**: Next.js 14 + Express.js
- **Version**: 1.0.0
- **Last Updated**: 2024-12-09

## Purpose and Functionality

### Business Purpose
The Authentication module provides secure user authentication and role-based authorization for the multi-tenant Ryvyl Cards platform. It supports different dashboard types (cardholder, corporate, manager, admin) with granular permission systems.

### Core Features
1. **JWT-based Authentication**
   - Token generation and validation
   - Secure token storage in HTTP-only cookies
   - Token refresh mechanism

2. **Multi-Dashboard Access Control**
   - Cardholder dashboard access
   - Corporate management interface
   - Programme manager interface
   - Lite admin interface

3. **Role-Based Permissions**
   - Dynamic permission assignment
   - Permission audit trails
   - Dashboard-specific permission sets

4. **Session Management**
   - Auto-logout functionality
   - Inactivity detection
   - Session timeout configuration

### Target Users
- **Cardholders**: Individual users managing personal cards
- **Corporate Managers**: Business users managing employee cards
- **Programme Managers**: Administrative users overseeing programmes
- **System Administrators**: Platform administrators

## Technical Architecture

### Architectural Pattern
**Hybrid Client-Server Authentication** with JWT tokens and session management

### Key Components

#### 1. JWT Utilities (`src/utils/auth-utils.ts`)
```typescript
export interface UserJwtPayload {
    id: string
    email: string
    dashboard?: string
    requires2FA?: boolean
    iat: number
    exp: number
}

export async function verifyToken(token: string): Promise<UserJwtPayload | null>
export async function isAuthenticated(token: string): Promise<boolean>
export async function hasAccess(token: string, requiredDashboard: string): Promise<boolean>
```

#### 2. Axios Authentication Interceptor (`src/utils/axiosInstance.js`)
```javascript
// Request interceptor adds auth headers
axiosInstance.interceptors.request.use((config) => {
    const token = getTokenFromCookie()
    if (token) {
        config.headers["Authorization"] = `Bearer ${token}`
    }
    return config
})

// Response interceptor handles auth errors
axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            removeTokenCookie()
        }
        return Promise.reject(error)
    }
)
```

#### 3. Authentication Guards (`src/components/with-auth.tsx`)
```typescript
const withAuth = (WrappedComponent: React.ComponentType) => {
    const Wrapper = (props: any) => {
        useEffect(() => {
            const checkAuth = async () => {
                try {
                    const response = await axiosInstance.get('/users/me')
                    if (!response.data) {
                        router.push('/login')
                    }
                } catch (error) {
                    router.push('/login')
                }
            }
            checkAuth()
        }, [router])
        return <WrappedComponent {...props} />
    }
    return Wrapper
}
```

### Data Flow
1. User submits login credentials
2. Express server validates credentials
3. JWT token generated and returned
4. Token stored in HTTP-only cookie
5. Subsequent requests include token in Authorization header
6. Middleware validates token on each request
7. Dashboard-specific access control applied

### Performance Characteristics
- **Token Validation**: ~1-2ms per request
- **Database Queries**: 1 query per authentication check
- **Memory Usage**: Minimal (stateless JWT)
- **Scalability**: Horizontal scaling supported

## Dependencies and Integrations

### External Libraries
```json
{
  "jose": "^6.0.11",           // JWT verification
  "axios": "^1.9.0",           // HTTP client
  "jsonwebtoken": "^9.0.2",    // JWT generation (server-side)
  "bcrypt": "^5.1.1",          // Password hashing
  "passport": "^0.7.0",        // Authentication middleware
  "passport-local": "^1.0.0"   // Local authentication strategy
}
```

### Internal Dependencies
- **Redux Store**: User state management
- **Next.js Middleware**: Request interception
- **Express Routes**: Authentication endpoints
- **MongoDB**: User data storage

### API Integrations
- **POST /api/users/login**: User authentication
- **POST /api/auth/logout**: Session termination
- **GET /api/users/me**: Current user information
- **POST /api/auth/refresh**: Token refresh

### Database Dependencies
- **Users Collection**: User credentials and profiles
- **Sessions Collection**: Active session tracking
- **Activity Collection**: Authentication audit logs

## Development Info

### Build Process
```bash
# Development
npm run dev

# Production build
npm run build
npm start
```

### Testing
**Status**: ❌ **No tests currently implemented**

**Required Test Coverage**:
- Unit tests for JWT utilities
- Integration tests for authentication flow
- E2E tests for login/logout scenarios

### Linting and Code Quality
```json
// .eslintrc.json
{
  "extends": ["next/core-web-vitals"],
  "rules": {
    // Custom rules needed for auth module
  }
}
```

### Setup Instructions
1. Configure environment variables:
   ```env
   JWT_SECRET=your-secret-key-minimum-32-characters
   DATABASE_URL=mongodb://localhost:27017/ryvyl-beta
   ```

2. Initialize authentication routes in Express server
3. Configure Next.js middleware for token validation

## Deployment & Operations

### CI/CD Pipeline
**Status**: ❌ **Not implemented**

**Recommended Pipeline**:
1. Code quality checks
2. Security vulnerability scanning
3. Authentication flow testing
4. Deployment to staging
5. Production deployment

### Configuration Management
```typescript
// Environment variables
const config = {
    jwtSecret: process.env.JWT_SECRET,
    tokenExpiry: process.env.JWT_EXPIRY || '7d',
    cookieSecure: process.env.NODE_ENV === 'production',
    sessionTimeout: process.env.SESSION_TIMEOUT || 300000 // 5 minutes
}
```

### Scaling Considerations
- **Stateless Design**: JWT tokens enable horizontal scaling
- **Database Optimization**: Index user lookup fields
- **Caching**: Implement Redis for session data
- **Load Balancing**: Sticky sessions not required

### Environment Variables
```env
# Required
JWT_SECRET=                    # JWT signing secret (min 32 chars)
DATABASE_URL=                  # MongoDB connection string

# Optional
JWT_EXPIRY=7d                 # Token expiration time
SESSION_TIMEOUT=300000        # Session timeout in milliseconds
COOKIE_SECURE=true            # Secure cookie flag
```

## Monitoring & Health

### Logging
**Current Implementation**: Basic console logging
```typescript
console.error("Token verification failed:", error)
```

**Recommended Enhancement**:
```typescript
import winston from 'winston'

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.json(),
    transports: [
        new winston.transports.File({ filename: 'auth-error.log', level: 'error' }),
        new winston.transports.File({ filename: 'auth-combined.log' })
    ]
})
```

### Metrics
**Recommended Metrics**:
- Authentication success/failure rates
- Token validation performance
- Session duration analytics
- Failed login attempt tracking

### Health Checks
```typescript
// Recommended health check endpoint
export async function GET() {
    try {
        // Verify JWT secret is configured
        if (!process.env.JWT_SECRET) {
            throw new Error('JWT_SECRET not configured')
        }
        
        // Test database connectivity
        await connectToDatabase()
        
        return NextResponse.json({ status: 'healthy' })
    } catch (error) {
        return NextResponse.json({ status: 'unhealthy', error: error.message }, { status: 500 })
    }
}
```

### Alerts
**Recommended Alerts**:
- High authentication failure rates
- JWT secret rotation needed
- Database connection failures
- Unusual login patterns

## Database Usage

### Collections Used
1. **users**: User credentials and profile data
2. **sessions**: Active session tracking (if implemented)
3. **activities**: Authentication audit logs

### Schema Design
```javascript
// User schema (inferred)
{
    _id: ObjectId,
    email: String,
    password: String, // Hashed
    dashboard: String, // "cardholder" | "corporate" | "programmeManager" | "admin"
    recordId: String,
    roles: Array,
    permissions: Object,
    createdAt: Date,
    updatedAt: Date
}
```

### Indexes
**Required Indexes**:
```javascript
// Users collection
db.users.createIndex({ email: 1 }, { unique: true })
db.users.createIndex({ dashboard: 1, recordId: 1 })

// Activities collection
db.activities.createIndex({ userId: 1, timestamp: -1 })
```

## Security Considerations

### Current Security Measures
1. **JWT Token Security**
   - Signed tokens with secret key
   - Expiration time enforcement
   - HTTP-only cookie storage

2. **Password Security**
   - bcrypt hashing
   - Salt rounds configuration

### Security Vulnerabilities
1. **🔴 Critical**: JWT secret not validated at startup
2. **🔴 Critical**: No rate limiting on authentication endpoints
3. **🟠 High**: Client-side authentication checks only
4. **🟠 High**: No CSRF protection
5. **🟡 Medium**: Session fixation possible

### Recommended Security Enhancements
```typescript
// 1. JWT secret validation
if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters')
}

// 2. Rate limiting
import rateLimit from 'express-rate-limit'
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: 'Too many authentication attempts'
})

// 3. CSRF protection
import csrf from 'csurf'
const csrfProtection = csrf({ cookie: true })
```

## Operational Procedures

### Startup Procedure
1. Verify environment variables
2. Test database connectivity
3. Validate JWT secret configuration
4. Initialize authentication routes

### Shutdown Procedure
1. Complete pending authentication requests
2. Close database connections
3. Clear in-memory session data

### Troubleshooting Guide
**Common Issues**:
1. **"JWT_SECRET not defined"**: Configure environment variable
2. **"Token verification failed"**: Check token format and secret
3. **"Database connection failed"**: Verify MongoDB connectivity
4. **"Unauthorized access"**: Check user permissions and dashboard access

## APIs & Integration Points

### Authentication Endpoints
```typescript
// Login
POST /api/users/login
Body: { email: string, password: string }
Response: { token: string, user: UserData }

// Logout
POST /api/auth/logout
Headers: { Authorization: "Bearer <token>" }
Response: { success: boolean }

// Current User
GET /api/users/me
Headers: { Authorization: "Bearer <token>" }
Response: { user: UserData }
```

### Integration Contracts
**Token Format**:
```json
{
  "id": "user_id",
  "email": "<EMAIL>",
  "dashboard": "cardholder",
  "requires2FA": false,
  "iat": **********,
  "exp": **********
}
```

## Development Context for AI Agents

### Extension Points
1. **Custom Authentication Providers**: Add OAuth, SAML, etc.
2. **Permission Systems**: Extend role-based permissions
3. **Multi-Factor Authentication**: Add 2FA/MFA support
4. **Session Management**: Implement advanced session controls

### Common Patterns
```typescript
// Authentication check pattern
const checkAuth = async () => {
    try {
        const response = await axiosInstance.get("users/me")
        if (response.data && response.data.dashboard === requiredDashboard) {
            // User authenticated and authorized
        } else {
            router.push("/login")
        }
    } catch (error) {
        router.push("/login")
    }
}
```

### Impact of Changes
- **JWT Secret Changes**: Requires all users to re-authenticate
- **Permission Changes**: May affect user access across dashboards
- **Database Schema Changes**: Requires migration planning
- **Authentication Flow Changes**: Impacts all user interactions

## Ownership & Contact

### Responsible Team
- **Primary**: Development Team
- **Secondary**: Security Team
- **Escalation**: Technical Lead

### Subject Matter Experts
- **Authentication**: Senior Backend Developer
- **Security**: Security Engineer
- **Frontend Integration**: Frontend Lead

### Documentation Links
- **API Documentation**: Not available
- **Security Guidelines**: Not available
- **Deployment Guide**: Not available

---

**Module Status**: 🟡 **Functional but requires security improvements**
**Security Rating**: 🔴 **High Risk** (multiple critical vulnerabilities)
**Maintenance Priority**: 🔴 **Critical** (immediate attention required)
