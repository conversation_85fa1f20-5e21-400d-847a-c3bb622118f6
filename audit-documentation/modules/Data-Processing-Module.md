# Data Processing Module Documentation

## Module Identity

- **Module Name**: Data Processing & File Management System
- **Type**: Core Business Logic Module
- **Repository**: Ryvyl Cards Monorepo
- **Framework**: Next.js 14 API Routes + MongoDB
- **Version**: 1.0.0
- **Last Updated**: 2024-12-09

## Purpose and Functionality

### Business Purpose
The Data Processing module handles large-scale data import, validation, transformation, and storage operations for delivery methods, postal codes, and card-related data. It supports bulk operations for Polish Post and DHL delivery data management.

### Core Features
1. **File Upload & Processing**
   - Excel/CSV file parsing
   - Multi-sheet data extraction
   - Bulk data validation
   - Progress tracking

2. **Data Validation & Transformation**
   - Schema validation
   - Data sanitization
   - Duplicate detection
   - Error reporting

3. **Database Operations**
   - Bulk insert operations
   - Metadata management
   - Query optimization
   - Data retrieval

4. **Export Capabilities**
   - PDF generation
   - Excel export
   - Data filtering
   - Custom reporting

### Target Users
- **Data Administrators**: Managing delivery method databases
- **Business Analysts**: Importing and analyzing data
- **System Operators**: Monitoring data processing operations

## Technical Architecture

### Architectural Pattern
**ETL (Extract, Transform, Load) Pipeline** with REST API endpoints

### Key Components

#### 1. File Processing APIs
```typescript
// Save records endpoint
POST /api/save-records
Body: {
    records: Array<{
        fileName: string,
        sheetName: string,
        headers: string[],
        rowData: any[],
        rowIndex: number,
        totalRows: number
    }>,
    metadata?: object
}

// Enhanced save with validation
POST /api/save-records-enhanced
// Similar structure with additional validation

// DHL-specific processing
POST /api/save-dhl-records
```

#### 2. Data Retrieval APIs
```typescript
// Get records with filtering
GET /api/get-records?fileName=X&limit=100&skip=0&search=term

// Get DHL records
GET /api/get-dhl-records

// Country-specific delivery methods
GET /api/get-country-delivery-methods?country=Poland
```

#### 3. Data Validation APIs
```typescript
// Check for duplicates
POST /api/check-dhl-duplicates

// Validate Polish Post data
POST /api/validate-polish-post
```

### Data Flow
1. **Upload Phase**
   - File uploaded via frontend
   - File parsed into records
   - Initial validation performed

2. **Processing Phase**
   - Records validated against schema
   - Duplicates detected and flagged
   - Data transformation applied

3. **Storage Phase**
   - Metadata saved to database
   - Records bulk inserted
   - Indexes updated

4. **Retrieval Phase**
   - Query optimization applied
   - Results filtered and paginated
   - Response formatted

### Performance Characteristics
- **Bulk Insert**: ~1000 records/second
- **File Processing**: Depends on file size and complexity
- **Memory Usage**: Optimized for large datasets
- **Database Queries**: Indexed for performance

## Dependencies and Integrations

### External Libraries
```json
{
  "xlsx": "^0.18.5",           // Excel file processing (VULNERABLE)
  "mongodb": "^6.18.0",        // Database driver
  "mongoose": "^8.17.2",       // ODM
  "jspdf": "^3.0.1",          // PDF generation
  "jspdf-autotable": "^5.0.2" // PDF table generation
}
```

### Internal Dependencies
- **MongoDB Connection**: Database operations
- **Authentication Module**: API security
- **Validation Utilities**: Data validation
- **Error Handling**: Centralized error management

### Database Collections
- **PocztaPost**: Polish postal delivery methods
- **DHLDeliveryMethods**: DHL delivery options
- **FileMetadata**: Upload metadata tracking
- **Activities**: Processing audit logs

## Development Info

### Build Process
```bash
# Development
npm run dev

# Production build
npm run build
npm start
```

### Testing
**Status**: ❌ **No tests currently implemented**

**Required Test Coverage**:
- Unit tests for validation functions
- Integration tests for API endpoints
- Performance tests for bulk operations
- Error handling tests

### Code Quality Issues
1. **🔴 Critical**: `@ts-nocheck` directive used
2. **🔴 Critical**: No input sanitization
3. **🟠 High**: No authentication on API endpoints
4. **🟡 Medium**: Basic validation only

### Setup Instructions
1. Configure MongoDB connection
2. Set up file upload directories
3. Configure validation schemas
4. Initialize database indexes

## Deployment & Operations

### Configuration Management
```typescript
// Database configuration
const config = {
    mongoUri: process.env.DATABASE_URL,
    dbName: "ryvyl-beta",
    collections: {
        pocztaPost: "PocztaPost",
        dhlMethods: "DHLDeliveryMethods",
        metadata: "FileMetadata"
    }
}
```

### Scaling Considerations
- **Database Sharding**: For large datasets
- **Background Processing**: For heavy operations
- **Caching**: For frequently accessed data
- **Load Balancing**: For high throughput

### Environment Variables
```env
DATABASE_URL=mongodb://localhost:27017/ryvyl-beta
MAX_FILE_SIZE=50MB
BATCH_SIZE=1000
PROCESSING_TIMEOUT=300000
```

## Monitoring & Health

### Current Logging
```typescript
console.error("Error in save-records API:", error)
console.log("Connected to MongoDB")
```

### Recommended Monitoring
- **Processing Times**: Track operation duration
- **Error Rates**: Monitor validation failures
- **Database Performance**: Query execution times
- **Memory Usage**: Monitor for memory leaks

### Health Checks
```typescript
// Database connectivity check
export async function GET() {
    try {
        await connectToDatabase()
        return NextResponse.json({ status: 'healthy' })
    } catch (error) {
        return NextResponse.json({ status: 'unhealthy' }, { status: 500 })
    }
}
```

## Database Usage

### Schema Design
```typescript
export interface PocztaPostRecord {
    _id?: string
    [key: string]: any  // Dynamic fields from uploaded data
    _fileName: string
    _sheetName: string
    _rowIndex: number
    _totalRows: number
    _uploadedAt: Date
    _createdAt: Date
    _updatedAt: Date
}
```

### Indexes
```javascript
// Performance indexes
db.PocztaPost.createIndex({ "_fileName": 1, "_sheetName": 1 })
db.PocztaPost.createIndex({ "_uploadedAt": -1 })
db.DHLDeliveryMethods.createIndex({ "country": 1, "serviceType": 1 })
```

### Query Patterns
```typescript
// Filtered retrieval
const filter = fileName ? { _fileName: fileName } : {}
if (search && searchField) {
    filter[searchField] = { $regex: search, $options: 'i' }
}

const records = await collection
    .find(filter)
    .skip(skip)
    .limit(limit)
    .toArray()
```

## Security Considerations

### Current Vulnerabilities
1. **🔴 Critical**: No authentication on API endpoints
2. **🔴 Critical**: No input validation/sanitization
3. **🔴 Critical**: Vulnerable xlsx dependency
4. **🟠 High**: No rate limiting
5. **🟠 High**: Potential NoSQL injection

### Security Recommendations
```typescript
// 1. Add authentication middleware
import { verifyToken } from '@/utils/auth-utils'

export async function POST(request: NextRequest) {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    const user = await verifyToken(token)
    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    // Continue with processing
}

// 2. Input validation with Zod
import { z } from 'zod'

const recordSchema = z.object({
    fileName: z.string().min(1).max(255),
    sheetName: z.string().min(1).max(255),
    headers: z.array(z.string()),
    rowData: z.array(z.any()),
    rowIndex: z.number().int().min(0),
    totalRows: z.number().int().min(1)
})

// 3. Rate limiting
import rateLimit from 'express-rate-limit'
const uploadLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10 // 10 uploads per window
})
```

## APIs & Integration Points

### Data Processing Endpoints
```typescript
// Save records
POST /api/save-records
Headers: { Authorization: "Bearer <token>" }
Body: { records: Record[], metadata?: object }
Response: { success: boolean, savedCount: number, metadataId?: string }

// Get records
GET /api/get-records?fileName=X&limit=100&skip=0
Headers: { Authorization: "Bearer <token>" }
Response: { success: boolean, records: Record[], total: number }

// Validate data
POST /api/validate-polish-post
Headers: { Authorization: "Bearer <token>" }
Body: { records: Record[] }
Response: { valid: boolean, errors: ValidationError[] }
```

### Integration Contracts
**Record Format**:
```typescript
interface ProcessingRecord {
    fileName: string
    sheetName: string
    headers: string[]
    rowData: any[]
    rowIndex: number
    totalRows: number
}
```

**Response Format**:
```typescript
interface ApiResponse<T> {
    success: boolean
    data?: T
    error?: string
    timestamp: string
}
```

## Operational Procedures

### Data Import Process
1. **Pre-validation**: Check file format and size
2. **Parsing**: Extract data from Excel/CSV
3. **Validation**: Apply business rules
4. **Transformation**: Clean and normalize data
5. **Storage**: Bulk insert to database
6. **Post-processing**: Update indexes and metadata

### Error Recovery
1. **Validation Errors**: Return detailed error messages
2. **Database Errors**: Implement retry logic
3. **Memory Errors**: Process in smaller batches
4. **Timeout Errors**: Implement background processing

### Maintenance Tasks
- **Index Optimization**: Regular index maintenance
- **Data Cleanup**: Remove old temporary data
- **Performance Monitoring**: Track query performance
- **Backup Verification**: Ensure data integrity

## Development Context for AI Agents

### Extension Points
1. **New Data Sources**: Add support for additional file formats
2. **Validation Rules**: Implement custom validation logic
3. **Transformation Pipelines**: Add data transformation steps
4. **Export Formats**: Support additional export formats

### Common Patterns
```typescript
// Validation pattern
function validateRecord(record: any): string | null {
    if (typeof record !== "object" || record === null) {
        return "Record must be an object"
    }
    // Additional validation logic
    return null
}

// Bulk processing pattern
const transformedRecords = records.map((record: any) => ({
    ...record,
    _fileName: fileName,
    _uploadedAt: new Date(),
    _createdAt: new Date()
}))

const savedIds = await saveRecordsToMongoDB(transformedRecords)
```

### Impact of Changes
- **Schema Changes**: Require database migration
- **Validation Changes**: May affect existing data
- **API Changes**: Impact frontend integration
- **Performance Changes**: Affect user experience

## Ownership & Contact

### Responsible Team
- **Primary**: Backend Development Team
- **Secondary**: Data Engineering Team
- **Escalation**: Technical Architecture Team

### Subject Matter Experts
- **Data Processing**: Senior Backend Developer
- **Database Optimization**: Database Administrator
- **File Processing**: Full-Stack Developer

---

**Module Status**: 🔴 **Critical Issues** (security vulnerabilities)
**Security Rating**: 🔴 **High Risk** (no authentication, vulnerable dependencies)
**Performance Rating**: 🟡 **Moderate** (needs optimization)
**Maintenance Priority**: 🔴 **Immediate** (security fixes required)
