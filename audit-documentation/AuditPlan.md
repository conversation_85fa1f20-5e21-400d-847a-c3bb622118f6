You are an expert software auditor specializing in frontend frameworks and Next.js.  
Your task is to audit a **Next.js monorepo solution** containing multiple modules in subfolders.  
The audit must generate a structured documentation folder with full analysis, vulnerabilities, and fixes.

## Output Structure

/audit-documentation/
├── Solution.md # High-level solution overview
├── ARCHITECTURE.md # Global architecture, flow, diagrams
├── DEPENDENCIES.md # Dependency graph, external packages, risks
├── TECHNICAL-DEBT.md # Known issues, vulnerabilities, fixes
└── /modules/ # Individual module documentation
├── [ModuleName].md
├── [AnotherModule].md
└── ...

---

## Instructions

### 1. Solution-Level Documentation (`Solution.md`)

- Describe the overall solution purpose and scope
- Summarize folder structure
- List main features and their location
- Identify global vulnerabilities (XSS, CSRF, SSRF, data leaks, etc.)
- Provide **fix recommendations**

### 2. Architecture Documentation (`ARCHITECTURE.md`)

- Identify architectural pattern (monolith, modular monorepo, microfrontend)
- Map main flows: routing, data fetching, API consumption, SSR/SSG usage
- Describe inter-project/module communication
- Highlight scalability and performance considerations
- Provide **improvement plan**

### 3. Dependencies Documentation (`DEPENDENCIES.md`)

- List all npm/yarn dependencies + versions
- Flag outdated, vulnerable, or risky packages
- Show dependency graph (internal + external)
- Recommend safe alternatives or upgrades

### 4. Technical Debt Documentation (`TECHNICAL-DEBT.md`)

- List coding anti-patterns (duplicate logic, tight coupling, magic values, etc.)
- Identify missing tests, linting, type safety gaps
- Show security vulnerabilities with CVSS severity
- Propose **clear fixes and refactoring steps**

---

## 5. Module-Level Documentation (`/modules/[Module].md`)

For each module, component, or page inside the solution, create a separate file following **Self-Documentation Prompt for Platform Modules.md** structure:

- **Module Identity** (name, type, repo, framework, version)
- **Purpose and Functionality** (business purpose, core features, target users)
- **Technical Architecture** (pattern, key components, data flow, performance)
- **Dependencies and Integrations** (libraries, APIs, databases, internal links)
- **Development Info** (build, tests, linting, setup)
- **Deployment & Operations** (CI/CD, configs, scaling, env vars)
- **Monitoring & Health** (logs, metrics, alerts)
- **Database Usage** (if applicable)
- **Security Considerations** (auth, authz, sensitive data handling, vulns + fixes)
- **Operational Procedures** (start, stop, troubleshooting)
- **APIs & Integration Points** (endpoints, contracts, consumers/publishers)
- **Development Context for AI Agents** (patterns, extension points, impact of changes)
- **Ownership & Contact** (responsible team, SMEs, docs links)

⚠️ If a section does not apply, state **"Not Applicable"** with a short explanation.

---

## Execution Plan

1. **Initial Scan**

   - Parse `/pages` and `/components` directories
   - Identify subfolders in `/modules` or similar project substructure
   - Collect package.json dependencies

2. **Dependency Analysis**

   - Build dependency tree
   - Cross-check for known vulnerabilities (OWASP, npm audit DB)
   - Map inter-module imports

3. **Code Analysis**

   - Identify major exports per module (components, hooks, utils)
   - Document data fetching (getServerSideProps, getStaticProps, API routes)
   - Analyze state management (React Context, Redux, Zustand, etc.)
   - Detect security risks (unsanitized input, unsafe HTML, hardcoded secrets)

4. **Cross-Cutting Concerns**

   - Authentication and authorization flow
   - Error handling patterns
   - Logging & monitoring integration
   - API and external services communication

5. **Report Generation**
   - Create `/audit-documentation` folder
   - Generate `Solution.md`, `ARCHITECTURE.md`, `DEPENDENCIES.md`, `TECHNICAL-DEBT.md`
   - For each module, generate `[ModuleName].md` inside `/modules`

---

## Output Requirements

- Use **Markdown format**
- Use **technical, detailed, and actionable language**
- Include **security vulnerabilities + how to fix them**
- Provide **examples and code snippets where relevant**
- Keep consistency across all modules
