{"name": "ryvyl_cards", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/themes": "^3.2.1", "@reduxjs/toolkit": "^2.8.1", "@tanstack/react-table": "^8.21.3", "@tremor/react": "^3.18.7", "@types/axios": "^0.9.36", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "3.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "flowbite-react": "^0.10.2", "framer-motion": "^11.18.2", "fs": "0.0.1-security", "https": "^1.0.0", "intl-tel-input": "^25.3.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.476.0", "mime-types": "^2.1.35", "mongodb": "^6.16.0", "mongoose": "^8.14.2", "next": "^14.2.32", "next-connect": "^1.0.0", "nodemailer": "^6.10.1", "nodemon": "^3.1.10", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-input-mask": "^2.0.4", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-remove-scroll": "^2.6.3", "recharts": "^2.15.3", "sonner": "^1.7.4", "sweetalert2": "^11.21.0", "sweetalert2-react-content": "^5.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.4"}, "devDependencies": {"@types/express": "^5.0.1", "@types/mongoose": "^5.11.97", "@types/node": "^20.17.46", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@types/react-phone-number-input": "^3.1.37", "@types/react-redux": "^7.1.34", "eslint": "^8.57.1", "eslint-config-next": "14.2.13", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.3", "prisma": "^5.22.0", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5.8.3", "webpack": "^5.101.3"}, "prisma": {"schema": "src/prisma/schema.prisma"}}