# ---------------------------
# 1. Builder stage
# ---------------------------
    FROM node:20-alpine AS builder

    # Set working directory
    WORKDIR /app
    
    # Copy package.json and lock file
    COPY package*.json ./
    
    # Install dependencies
    RUN npm install --legacy-peer-deps
    
    # Copy all source
    COPY . .
    
    # Build Next.js project
    RUN npm run build
    
    # ---------------------------
    # 2. Runner stage
    # ---------------------------
    FROM node:20-alpine AS runner
    
    WORKDIR /app
    
    ENV NODE_ENV=
    ENV PORT=
    
    # Copy only the required files from builder
    COPY --from=builder /app/package*.json ./
    COPY --from=builder /app/node_modules ./node_modules
    COPY --from=builder /app/.next ./.next
    COPY --from=builder /app/public ./public
    COPY --from=builder /app/next.config.mjs ./next.config.mjs
    
    EXPOSE 22344
    
    CMD ["npm", "run", "start"]
    