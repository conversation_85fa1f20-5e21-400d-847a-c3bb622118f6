# ---------------------------
# 1. Builder stage
# ---------------------------
    FROM node:20-alpine AS builder

    WORKDIR /app
    
    COPY package*.json ./
    RUN npm install --legacy-peer-deps
    COPY . .
    
    # Pass build args into envs for Next.js
    ARG NEXT_PUBLIC_API_URL
    ARG NEXT_PUBLIC_ASSET_URL
    ARG NEXT_PUBLIC_DEFAULT_COMPANY_ID
    
    ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
    ENV NEXT_PUBLIC_ASSET_URL=$NEXT_PUBLIC_ASSET_URL
    ENV NEXT_PUBLIC_DEFAULT_COMPANY_ID=$NEXT_PUBLIC_DEFAULT_COMPANY_ID
    ENV NEXT_PUBLIC_PHYSICAL_CARD_LIMIT=5
    ENV NEXT_PUBLIC_VIRTUAL_CARD_LIMIT=6
    
    RUN npm run build
    
    # ---------------------------
    # 2. Runner stage
    # ---------------------------
    FROM node:20-alpine AS runner
    
    WORKDIR /app
    
    
    COPY --from=builder /app/package*.json ./
    COPY --from=builder /app/node_modules ./node_modules
    COPY --from=builder /app/.next ./.next
    COPY --from=builder /app/public ./public
    COPY --from=builder /app/next.config.mjs ./next.config.mjs
    
    EXPOSE 22344
    CMD ["npm", "run", "start"]
    