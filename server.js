require('dotenv').config();
const express = require('express');
const next = require('next');
const mongoose = require('mongoose');
const userRoutes = require('./src/routes/userRoutes');
const roleRoutes = require('./src/routes/roleRoutes');
const companyRoutes = require('./src/routes/companyRoutes');
const {join} = require("node:path");
const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();
const cors = require('cors');
const cookieParser = require('cookie-parser'); // To parse cookies

const server = express();

// Middleware to parse JSON request bodies
server.use(express.json());
server.use(cookieParser()); // For parsing cookies
server.use(cors());
// Function to connect to MongoDB
async function connectToMongoDB() {
    try {
        await mongoose.connect(process.env.DATABASE_URL);
        console.log('MongoDB connected');
    } catch (error) {
        console.error('Failed to connect to MongoDB', error);
    }
}

// Prepare Next.js app
app.prepare().then(() => {
    // Connect to MongoDB
    connectToMongoDB();


    server.use(cors({
        origin: 'http://localhost:3000'
    }));
    // Use routes
    server.use('/api/users', userRoutes);
    server.use('/api/roles', roleRoutes);
    server.use('/api/company', companyRoutes);



    // Catch-all handler to let Next.js handle all other routes
    server.all(/(.*)/, (req, res) => {
        return handle(req, res);
    });
    process.on('unhandledRejection', (reason, promise) => {
        console.error('Unhandled Rejection at:', promise, 'reason:', reason);
        // Application specific logging, throwing an error, or other logic here
    });

    // Start the server
    server.listen(process.env.PORT, (err) => {
        if (err) throw err;
        console.log('> Ready on http://localhost:3000');
    });
});


