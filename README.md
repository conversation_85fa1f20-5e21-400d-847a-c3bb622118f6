Here is a comprehensive `README.md` for your project, covering setup, usage, tech stack, and contribution guidelines.


# Ryvyl Cards

A modern web application for card management, built with React, Next.js, TypeScript, and a robust backend using Express and MongoDB/Mongoose.

## Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Getting Started](#getting-started)
- [Development](#development)
- [Scripts](#scripts)
- [Environment Variables](#environment-variables)
- [Project Structure](#project-structure)
- [Testing](#testing)
- [Linting & Formatting](#linting--formatting)
- [Contributing](#contributing)
- [License](#license)

---

## Features

- Modern UI with React, Next.js, and Tailwind CSS
- Authentication with Passport.js and JWT
- Form validation with React Hook Form and Zod
- State management with Redux Toolkit
- Data visualization with Recharts and Tremor
- PDF export with jsPDF
- REST API with Express
- MongoDB and PostgreSQL support
- Email notifications with Nodemailer
- Comprehensive validation and error handling

## Tech Stack

- **Frontend:** React, Next.js, Tailwind CSS, Radix U<PERSON>, Tremor, Framer Motion
- **Backend:** Express, Mongoose, MongoDB, PostgreSQL
- **State Management:** Redux Toolkit
- **Validation:** Zod, React Hook Form, Express Validator
- **Authentication:** Passport.js, JWT
- **Testing:** Jest, React Testing Library (add as needed)
- **Other:** Nodemailer, Axios, dotenv

## Getting Started

### Prerequisites

- Node.js (v18+ recommended)
- Yarn or npm
- MongoDB and/or PostgreSQL instance

### Installation

Clone the repository:

```sh
git clone https://gitlab.com/stratifiedprojects/ryvyl-cards.git
cd ryvyl-cards
```

Install dependencies:

```sh
yarn install
# or
npm install
```

### Environment Setup

Copy the example environment file and update values as needed:

```sh
cp .env.example .env
```

Set up your database and update the connection strings in `.env`.

### Database Migration

For Mongoose (MongoDB), ensure your MongoDB instance is running.

## Development

Start the development server:

```sh
yarn dev
# or
npm run dev
```

The app will be available at `http://localhost:3000`.

## Scripts

- `yarn dev` — Start Next.js in development mode
- `yarn build` — Build the app for production
- `yarn start` — Start the production server
- `yarn lint` — Run ESLint

## Environment Variables

See `.env.example` for all required environment variables, including:

- `DATABASE_URL`
- `MONGODB_URI`
- `JWT_SECRET`
- `EMAIL_USER`, `EMAIL_PASS` (for Nodemailer)
- Others as needed

## Project Structure

```
ryvyl-cards-frontend/
├── Card Images/
│   ├── Business front.png
│   ├── Businesss back.png
│   ├── pp-bd.png
│   ├── pp-fd.png
│   ├── prepaid  back.png
│   ├── prepaid  front.png
│   ├── visa debit phy card back.png
│   ├── visa debit phy card.png
│   ├── visa debit virtual card back-1.png
│   ├── visa debit virtual card back.png
│   ├── visa debit virtual card-1.png
│   └── visa debit virtual card.png
├── public/
│   ├── bg-about1.png
│   ├── logo.jpeg
│   ├── sw.js
│   ├── cards/
│   │   ├── back-vp.png
│   │   ├── physical-back.png
│   │   ├── physical-front.png
│   │   ├── pp-bd.svg
│   │   └── pp-fd.svg
│   ├── images/
│   │   ├── card.jpg
│   │   ├── card.svg
│   │   ├── logo-big.png
│   │   ├── placeholder.jpg
│   │   └── logo/
│   └── static/
│       └── i.txt
├── src/
│   ├── app/
│   │   ├── api/
│   │   ├── b2b/
│   │   ├── cardholder/
│   │   ├── corporate/
│   │   ├── favicon.ico
│   │   ├── fonts/
│   │   ├── forget-password/
│   │   ├── globals.css
│   │   ├── individual/
│   │   ├── layout.tsx
│   │   ├── lite/
│   │   ├── login/
│   │   ├── manager/
│   │   ├── onboarding/
│   │   ├── page-excel.tsx
│   │   ├── page-main.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── ActionButtons.tsx
│   │   ├── activity-logger.tsx
│   │   ├── ... (many more components)
│   │   ├── data-table/
│   │   └── ui/
│   ├── config/
│   │   └── database.js
│   ├── hooks/
│   │   ├── use-activity-tracking.tsx
│   │   ├── use-inactivity-timer.tsx
│   │   ├── use-mobile.tsx
│   │   └── use-toast.ts
│   ├── lib/
│   │   ├── all_countries.json
│   │   ├── ... (various utility files)
│   ├── middleware/
│   │   └── activityLogger.js
│   ├── models/
│   │   └── Activity.js
│   ├── routes/
│   │   └── analytics.js
│   ├── store/
│   │   ├── hooks.ts
│   │   ├── index.ts
│   │   └── userSlice.ts
│   ├── types/
│   │   └── types.ts
│   └── utils/
│       ├── alertHelper.ts
│       ├── auth-utils.ts
│       ├── axiosInstance.js
│       ├── country-data.ts
│       ├── data.ts
│       ├── helpers.ts
│       ├── mailer.js
│       └── pdfGenerator.ts
├── .env
├── .eslintrc.json
├── .gitignore
├── next-env.d.ts
├── next.config.mjs
├── package.json
├── postcss.config.mjs
├── qodana.yaml
├── README.md
├── Requirments.txt
├── server.js
├── tailwind.config.ts
├── test.html
├── tsconfig.json
└── yarn.lock
```

## Testing

Add and run tests with your preferred framework (e.g., Jest):

```sh
yarn test
```

## Linting & Formatting

Run ESLint:

```sh
yarn lint
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/YourFeature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/YourFeature`)
5. Create a Merge Request

## License

This project is private and not licensed for public use.
