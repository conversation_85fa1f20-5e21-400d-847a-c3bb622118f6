//@ts-nocheck
"use client"

import {Badge} from "@/components/ui/badge"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger} from "@/components/ui/tabs"
import { AlertTriangle, Building, Calendar, Copy, CreditCard, Download, Edit, FileText, Globe, Mail, MapPin, Package, Phone, Plus, RefreshCw, Trash2, Users, Save, CheckCircle2, Clock, Paperclip } from 'lucide-react'
import type React from "react"
import {memo, useCallback, useEffect, useMemo, useState} from "react"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {useRouter} from "next/navigation"
import Link from "next/link"
import {Skeleton} from "@/components/ui/skeleton"
import {Avatar, AvatarFallback} from "@/components/ui/avatar"
import {Separator} from "@/components/ui/separator"
import {ContactForm} from "@/components/add-contact-modal"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {alertHelper} from "@/utils/alertHelper"
import axiosInstance from "@/utils/axiosInstance"
import Swal from "sweetalert2"
import {country_currency} from "@/utils/data"
import PhoneNumberDisplay from "@/components/PhoneDispaly"
import {MdEmail} from "react-icons/md"
import {Cross2Icon} from "@radix-ui/react-icons"
import {BinAllocationSummary} from "./bin-allocation-summary"
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from "@/components/ui/collapsible"
import { ChevronDown, X } from 'lucide-react'
import {useAppSelector} from "@/store/hooks";
import SendCredentialsDialog from "@/components/send-credentials-dialog"





// Helper function to format dates
const formatDate = (dateString: string) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
    })
}
const formatDateLocal = (dateString: string) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
    })
}

interface Document {
    name: string
    type: string
    size: string
}

interface Event {
    name: string
    type: string
    size: string
    title: string
}

const documents: Document[] = [
    {name: "nameofdocument", type: "doctype", size: "2.1MB"},
    {name: "nameofdocument", type: "doctype", size: "2.1MB"},
    {name: "nameofdocument", type: "doctype", size: "2.1MB"},
]

interface ReviewStatus {
    icon: React.ReactNode
    label: string
    actionRequired?: boolean
}




const InfoItem = ({label, value, icon}: { label: string; value: React.ReactNode; icon?: React.ReactNode }) => (
    <div className="flex items-start gap-3">
        {icon && <div className="mt-0.5 text-muted-foreground">{icon}</div>}
        <div>
            <p className="text-sm font-medium text-muted-foreground">{label}</p>
            <div className="mt-1 font-medium">{value}</div>
        </div>
    </div>
)

const AddressDetails = memo(({address, title}: { address: any; title: string }) => {
    if (!address) return null
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">{title}</h3>
            <div className="grid gap-4 md:grid-cols-2">
                <InfoItem label="Street Address" value={address.street} icon={<MapPin size={16}/>}/>
                {address.building_number && (
                    <InfoItem label="Building Number" value={address.building_number} icon={<Building size={16}/>}/>
                )}
                {address.apartment_number && (
                    <InfoItem label="Apartment Number" value={address.apartment_number} icon={<Building size={16}/>}/>
                )}
                <InfoItem label="City" value={address.city} icon={<MapPin size={16}/>}/>
                {address.state && <InfoItem label="State/Province" value={address.state} icon={<MapPin size={16}/>}/>}
                <InfoItem label="Postal Code" value={address.postal_code} icon={<MapPin size={16}/>}/>
                <InfoItem label="Country" value={address.country} icon={<MapPin size={16}/>}/>
            </div>
        </div>
    )
})

AddressDetails.displayName = "AddressDetails"

const DocumentItem = ({name, type, size}: { name: string; type: string; size: string }) => (
    <div className="flex items-center justify-between p-3 rounded-lg border group hover:bg-muted/50 transition-colors">
        <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-md text-primary">
                <FileText size={18}/>
            </div>
            <div>
                <p className="font-medium">{name}</p>
                <p className="text-xs text-muted-foreground">
                    {type} • {size}
                </p>
            </div>
        </div>
        <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
            <Download size={16}/>
        </Button>
    </div>
)

const StatusBadge = ({status}: { status: string }) => {
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case "approved":
                return "bg-green-100 text-green-700 border-green-200"
            case "pending":
                return "bg-yellow-100 text-yellow-700 border-yellow-200"
            case "rejected":
                return "bg-red-100 text-red-700 border-red-200"
            case "medium":
                return "bg-yellow-100 text-yellow-700 border-yellow-200"
            default:
                return "bg-gray-100 text-gray-700 border-gray-200"
        }
    }
    return (
        <Badge variant="outline" className={`w-fit ${getStatusColor(status)}`}>
            {status}
        </Badge>
    )
}

const LoadingState = () => (
    <div className="w-full space-y-6">
        <Skeleton className="h-8 w-64"/>
        <div className="grid lg:grid-cols-[2fr,1fr] gap-6">
            <div className="space-y-6">
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-48"/>
                        <Skeleton className="h-4 w-32"/>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {Array(6)
                                .fill(0)
                                .map((_, i) => (
                                    <div key={i} className="flex justify-between">
                                        <Skeleton className="h-4 w-32"/>
                                        <Skeleton className="h-4 w-48"/>
                                    </div>
                                ))}
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-32"/>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {Array(4)
                                .fill(0)
                                .map((_, i) => (
                                    <Skeleton key={i} className="h-16 w-full"/>
                                ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
            <div className="space-y-6">
                {Array(3)
                    .fill(0)
                    .map((_, i) => (
                        <Card key={i}>
                            <CardHeader>
                                <Skeleton className="h-6 w-40"/>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {Array(3)
                                        .fill(0)
                                        .map((_, j) => (
                                            <Skeleton key={j} className="h-4 w-full"/>
                                        ))}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
            </div>
        </div>
    </div>
)

const ErrorState = ({error, retry}: { error: string; retry: () => void }) => (
    <div className="w-full flex flex-col items-center justify-center p-12">
        <div className="p-4 rounded-full bg-red-100 text-red-600 mb-4">
            <AlertTriangle size={32}/>
        </div>
        <h2 className="text-xl font-semibold mb-2">Error Loading Company Data</h2>
        <p className="text-muted-foreground mb-6 text-center max-w-md">{error}</p>
        <Button onClick={retry} className="flex items-center gap-2">
            <RefreshCw size={16}/>
            Retry
        </Button>
    </div>
)

export default function Company({params}: { params: { id: string } }) {
    const [companyData, setCompanyData] = useState<null | any>(null)
    const [cip, setCip] = useState<null | any>(null)
    const [contacts, setContacts] = useState<any[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [contactFormOpen, setContactFormOpen] = useState(false)
    const [selectedContact, setSelectedContact] = useState<any>(null)
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [contactToDelete, setContactToDelete] = useState<string | null>(null)
    const [accountBalance, setBalance] = useState(null)
    const [account, setAccount] = useState<null | any>(null)
    // Alert settings state
    const [criticalBinsLimit, setCriticalBinsLimit] = useState<string>("")
    const [alertSettingsLoading, setAlertSettingsLoading] = useState(false)
    const [isLoadingAlertSettings, setIsLoadingAlertSettings] = useState(false)
    const user = useAppSelector((state) => state.user.user)
    const [sendCredentialsOpen, setSendCredentialsOpen] = useState(false)


    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
    }, [user.roles])

    // Memoize allPermissions
    const allPermissions = useMemo(() => {
        return roles.flatMap((role) => role.permissions)
    }, [roles])

    // Memoize hasPermission
    const hasPermission = useCallback((permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        return allPermissions.includes(permission)
    }, [roles, allPermissions])

    // Memoize permission checks
    const userHasDisablePermission = useMemo(() => hasPermission("Programme Pipeline_Disable Company"), [hasPermission])
    const userHasEditPermission = useMemo(() => hasPermission("Programme Pipeline_Edit Company"), [hasPermission])
    const userHasAssignProductPermission = useMemo(() => hasPermission("Programme Pipeline_Assign Products"), [hasPermission])
    const userHasSendCredentialsPermission = useMemo(() => hasPermission("Programme Pipeline_Send Credentials"), [hasPermission])


    const router = useRouter()

    const fetchCompanyDetails = useCallback(async () => {
        try {
            setLoading(true)
            setError(null)
            const response = await axiosInstance.get(`/company/${params.id}`)
            setCompanyData(response.data.company)
            setCip(response.data.cip)
            setAccount(response.data.companyAccount[0])
            setContacts(response.data.contacts)

        } catch (error: any) {
            console.error("Error in fetchCompanyDetails:", error)
            setError("Failed to load company data. Please try again.")
        } finally {
            setLoading(false)
        }
    }, [params.id])

    // Update alert settings
    const updateAlertSettings = useCallback(async () => {
        if (!criticalBinsLimit || isNaN(Number(criticalBinsLimit))) {
            alertHelper.showToast("Please enter a valid number for critical bins limit", "error")
            return
        }
        try {
            setAlertSettingsLoading(true)
            const response = await axiosInstance.post(`/company/${params.id}/alert-settings`, {
                critical_bins: Number(criticalBinsLimit),
            })
            if (response.status === 200 || response.status === 201) {
                alertHelper.showToast("Alert settings updated successfully", "success")
            }
        } catch (error: any) {
            console.error("Error updating alert settings:", error)
            alertHelper.showToast(error.response?.data?.message || "Failed to update alert settings", "error")
        } finally {
            setAlertSettingsLoading(false)
        }
    }, [criticalBinsLimit, params.id])

    useEffect(() => {
        fetchCompanyDetails()
    }, [fetchCompanyDetails])

    const handleRowClick = useCallback((program: any) => {
        router.push(`/lite/admin/programmes/company/${params.id}/BIN-view`)
    }, [router, params.id])

    const handleAddContact = useCallback(() => {
        setSelectedContact(null)
        setContactFormOpen(true)
    }, [])

    const handleEditContact = useCallback((contact: any) => {
        setSelectedContact(contact)
        setContactFormOpen(true)
    }, [])

    const handleDeleteContact = useCallback((contactId: string) => {
        setContactToDelete(contactId)
        setDeleteDialogOpen(true)
    }, [])

    const confirmDeleteContact = useCallback(async () => {
        if (!contactToDelete) return
        try {
            setLoading(true)
            const response = await axiosInstance.delete(`contacts/${contactToDelete}`)
            if (response.status === 200) {
                alertHelper.showToast("The contact has been deleted successfully.", "success")
                setContacts((prevContacts) => prevContacts.filter((contact) => contact._id !== contactToDelete))
            }
            alertHelper.showToast("The contact has been deleted successfully.", "success")
        } catch (error: any) {
            console.error("Error deleting contact:", error)
            alertHelper.showToast("Failed to delete contact. Please try again.", "error")
        } finally {
            setLoading(false)
            setDeleteDialogOpen(false)
            setContactToDelete(null)
        }
    }, [contactToDelete])

    const refreshContacts = async (newContactData: any) => {
        if (selectedContact) {
            setContacts((prevContacts) =>
                prevContacts.map((contact) =>
                    contact._id === selectedContact._id ? {...contact, ...newContactData} : contact,
                ),
            )
            alertHelper.showToast("The contact has been updated successfully.", "success")
        } else {
            const newContact = {
                ...newContactData,
                _id: `contact_${Date.now()}`,
            }
            setContacts((prevContacts) => [...prevContacts, newContact])
            alertHelper.showToast("The contact has been added successfully.", "success")
        }
    }

    const handleUnassignProduct = async (productId: string) => {
        const result = await Swal.fire({
            title: "Unassign Product",
            text: "Are you sure you want to unassign this product?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#00a8a5",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, unassign!",
        })
        if (result.isConfirmed) {
            setLoading(true)
            try {
                const response = await axiosInstance.delete(`/cip/${productId}`)
                if (response.status === 200) {
                    setCip((prevCip) => prevCip.filter((product) => product._id !== productId))
                    alertHelper.showToast("Product unassigned successfully", "success")
                }
            } catch (error: any) {
                setError(error.response?.data.message || "Failed to unassign product")
                alertHelper.showToast(
                    "Failed to unassign product: " + (error.response?.data.message || "Unknown error"),
                    "error",
                )
            } finally {
                setLoading(false)
            }
        }
    }

    const getAccountBalance = async (account = "**********************") => {
        try {
            const response = await axiosInstance.get(`/legacy/fetch-balance/${account}`)
            setBalance(response.data.availableBalance)
        } catch (error) {
            console.error("Error fetching account balance:", error)
        }
    }

    if (loading && !companyData) {
        return <LoadingState/>
    }

    if (error) {
        return <ErrorState error={error} retry={fetchCompanyDetails}/>
    }
    const disableAccount = async (company_name, email, company_id) => {
        const result = await Swal.fire({
            title: "Confirm",
            text: `Would like to disable company (${company_name})?`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#00a8a5",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, Disable!",
        })
        if (result.isConfirmed) {
            setLoading(true)
            try {
                const response = await axiosInstance.post("company/disable", {
                    email: email,
                    id: company_id,
                })
                if (response.status === 200) {
                    alertHelper.showToast("Company & login is disabled!", "success")
                }
            } catch (e) {
                console.error(e)
                alertHelper.showToast(e.response.data.message, "error")
            } finally {
                setLoading(false)
            }
        }
    }

    const enableAccount = async (company_name, email, company_id) => {
        const result = await Swal.fire({
            title: "Confirm",
            text: `Would like to enable company (${company_name})?`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#00a8a5",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, enable!",
        })
        if (result.isConfirmed) {
            setLoading(true)
            try {
                const response = await axiosInstance.post("company/enable", {
                    email: email,
                    id: company_id,
                })
                if (response.status === 200) {
                    alertHelper.showToast("Company is enabled!", "success")
                }
            } catch (e) {
                console.error(e)
                alertHelper.showToast(e.response.data.message, "error")
            } finally {
                setLoading(false)
            }
        }
    }

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text)
        alertHelper.showToast("Copied to clipboard!", "success")
    }

    return (
        <div className="w-full space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold">{companyData?.company_name}</h1>
                    <p className="text-muted-foreground">ID: {companyData?.ryvyl_id}</p>
                </div>
                {user.dashboard === "infinity" && (
                    <div className="flex items-center gap-3">
                        {userHasSendCredentialsPermission && (
                            <Button
                                className="gap-2"
                                onClick={() => setSendCredentialsOpen(true)}
                            >
                                <MdEmail size={16}/>
                                Send Credentials
                            </Button>
                        )}
                        {userHasEditPermission && (
                            <Link href={`/lite/admin/companies/${params.id}/edit`} passHref>
                                <Button className="gap-2">
                                    <Edit size={16}/>
                                    Edit Company
                                </Button>
                            </Link>
                        )}
                        {userHasAssignProductPermission && (
                            <Link href={`/lite/admin/companies/${params.id}/cip/create`} passHref>
                                <Button className="gap-2">
                                    <Plus size={16}/>
                                    Assign Product
                                </Button>
                            </Link>
                        )}
                        {userHasDisablePermission && (
                            companyData.status.toLowerCase() !== "disabled" ? (
                                <Button
                                    className="gap-2 "
                                    variant="destructive"
                                    onClick={() => disableAccount(companyData?.company_name, companyData?.company_email, companyData?._id)}
                                >
                                    <Cross2Icon size={16}/>
                                    Disable Company
                                </Button>
                            ) : (
                                <Button
                                    className="gap-2 "
                                    onClick={() => enableAccount(companyData?.company_name, companyData?.company_email, companyData?._id)}
                                >
                                    <Cross2Icon size={16}/>
                                    Enable Company
                                </Button>
                            )
                        )}
                    </div>
                )}

            </div>

            <div className="grid lg:grid-cols-[2fr,1fr] gap-6">
                {/* Main Content */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Company Overview</CardTitle>
                            <CardDescription>View and manage company information</CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <Tabs defaultValue="details" className="w-full">
                                <div className="px-6">
                                    <TabsList
                                        className="w-full justify-start h-auto p-0 bg-transparent border-b rounded-none">
                                        <TabsTrigger
                                            value="details"
                                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                        >
                                            Details
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="addresses"
                                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                        >
                                            Addresses
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="contacts"
                                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                        >
                                            Contacts
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="products"
                                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                        >
                                            Products
                                        </TabsTrigger>


                                        <TabsTrigger
                                            value="pmtypes"
                                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                        >
                                            PM Types
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="accounts"
                                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                        >
                                            Accounts
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="summery"
                                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                        >
                                            BIN Allocations
                                        </TabsTrigger>
                                        {user.dashboard === "infinity" && (
                                            <TabsTrigger
                                                value="alert-settings"
                                                className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                            >
                                                BIN Alert Settings
                                            </TabsTrigger>
                                        )}

                                    </TabsList>
                                </div>

                                {/* Alert Settings Tab */}
                                <TabsContent value="alert-settings" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="flex items-center gap-2">
                                            <AlertTriangle className="h-5 w-5 text-primary"/>
                                            <h2 className="text-lg font-semibold">BIN Alert Settings</h2>
                                        </div>
                                        <p className="text-sm text-muted-foreground">
                                            Configure alert thresholds for this company's BIN allocations. An email
                                            alert will be sent when
                                            the number of critical BINs reaches the specified limit.
                                        </p>
                                        {isLoadingAlertSettings ? (
                                            <Card>
                                                <CardContent className="p-6">
                                                    <div className="space-y-4">
                                                        <Skeleton className="h-6 w-48"/>
                                                        <Skeleton className="h-10 w-full"/>
                                                        <Skeleton className="h-10 w-24"/>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        ) : (
                                            <Card>
                                                <CardHeader>
                                                    <CardTitle className="text-base">Critical BINs Email
                                                        Alert</CardTitle>
                                                    <CardDescription>
                                                        Set the threshold for critical BINs that will trigger an email
                                                        notification
                                                    </CardDescription>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="critical-bins-limit">Critical BINs Limit</Label>
                                                        <Input
                                                            id="critical-bins-limit"
                                                            type="number"
                                                            min="1"
                                                            placeholder="Enter limit (e.g., 10)"
                                                            value={companyData.critical_bins}
                                                            onChange={(e) => setCriticalBinsLimit(e.target.value)}
                                                            className="max-w-xs"
                                                        />
                                                        <p className="text-xs text-muted-foreground">
                                                            Email alert will be sent when critical BINs count reaches or
                                                            exceeds this number
                                                        </p>
                                                    </div>
                                                    <Button
                                                        onClick={updateAlertSettings}
                                                        disabled={alertSettingsLoading || !criticalBinsLimit}
                                                        className="flex items-center gap-2"
                                                    >
                                                        {alertSettingsLoading ? (
                                                            <RefreshCw className="h-4 w-4 animate-spin"/>
                                                        ) : (
                                                            <Save className="h-4 w-4"/>
                                                        )}
                                                        {alertSettingsLoading ? "Saving..." : "Save Settings"}
                                                    </Button>
                                                </CardContent>
                                            </Card>
                                        )}
                                        {/* Current Settings Display */}
                                        {!isLoadingAlertSettings && criticalBinsLimit && (
                                            <Card className="bg-blue-50 border-blue-200">
                                                <CardContent className="p-4">
                                                    <div className="flex items-start gap-3">
                                                        <div className="p-1 bg-blue-100 rounded-full">
                                                            <Mail className="h-4 w-4 text-blue-600"/>
                                                        </div>
                                                        <div>
                                                            <h4 className="font-medium text-blue-900">Current Alert
                                                                Configuration</h4>
                                                            <p className="text-sm text-blue-700 mt-1">
                                                                Email alerts will be triggered when this company
                                                                has{" "}
                                                                <span className="font-semibold">{criticalBinsLimit} or more</span> critical
                                                                BINs
                                                            </p>
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        )}
                                    </div>
                                </TabsContent>

                                {/* Accounts Tab */}
                                <TabsContent value="accounts" className="p-6">
                                    <div className="space-y-6">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <CreditCard className="h-5 w-5 text-primary"/>
                                                <h2 className="text-lg font-semibold">Account Details</h2>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="flex items-center gap-2 bg-transparent"
                                                onClick={() => getAccountBalance(account?.accountNumber)}
                                            >
                                                <RefreshCw className="h-4 w-4"/>
                                                Refresh Balance
                                            </Button>
                                        </div>
                                        <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-6">
                                            <div className="flex flex-col md:flex-row justify-between gap-6">
                                                <div>
                                                    <h3 className="text-sm text-muted-foreground mb-1">Available
                                                        Balance</h3>
                                                    <div className="text-3xl font-bold mb-2">
                                                        {Intl.NumberFormat("en-US", {
                                                            style: "currency",
                                                            currency:
                                                                country_currency.find((r) => r.numericCode === account?.currency)?.currencyCode ||
                                                                "EUR",
                                                        }).format(accountBalance)}
                                                    </div>
                                                    <div
                                                        className="text-sm text-muted-foreground">{account?.bankName || "Ryvyl EU"}</div>
                                                </div>
                                                <div className="space-y-3">
                                                    <div>
                                                        <span
                                                            className="text-sm text-muted-foreground block">IBAN</span>
                                                        <div className="flex items-center gap-2">
                                                            <span className="font-mono">{account?.accountNumber}</span>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                className="h-6 w-6"
                                                                onClick={() => copyToClipboard(account?.accountNumber)}
                                                            >
                                                                <Copy className="h-3.5 w-3.5"/>
                                                            </Button>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <span
                                                            className="text-sm text-muted-foreground block">SWIFT Code</span>
                                                        <span
                                                            className="font-mono">{account?.bankNumber || "Not Specified"}</span>
                                                    </div>
                                                    <div>
                                                        <span className="text-sm text-muted-foreground block">Account Name</span>
                                                        <span>
                              {companyData.admin_first_name} {companyData.admin_last_name}
                            </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="details" className="p-6 pt-4">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <InfoItem label="Company Name" value={companyData?.company_name}
                                                  icon={<Building size={16}/>}/>
                                        <InfoItem
                                            label="Country of Incorporation"
                                            value={companyData?.country_of_incorporation}
                                            icon={<MapPin size={16}/>}
                                        />
                                        <InfoItem
                                            label="Registration Number"
                                            value={companyData?.company_number}
                                            icon={<FileText size={16}/>}
                                        />
                                        <InfoItem
                                            label="Registration Date"
                                            value={formatDate(companyData?.registration_date)}
                                            icon={<Calendar size={16}/>}
                                        />
                                        <InfoItem label="Industry" value={companyData?.company_industry}
                                                  icon={<Building size={16}/>}/>
                                        <InfoItem
                                            label="Type of Business"
                                            value={companyData?.type_of_business}
                                            icon={<Building size={16}/>}
                                        />
                                        <InfoItem
                                            label="Website"
                                            value={
                                                <a
                                                    href={companyData?.company_website}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-primary hover:underline"
                                                >
                                                    {companyData?.company_website}
                                                </a>
                                            }
                                            icon={<Globe size={16}/>}
                                        />
                                        <InfoItem label="Client ID" value={companyData?.ryvyl_id}
                                                  icon={<FileText size={16}/>}/>
                                        <InfoItem
                                            label="Company Email"
                                            value={
                                                <a href={`mailto:${companyData?.company_email}`}
                                                   className="text-primary hover:underline">
                                                    {companyData?.company_email}
                                                </a>
                                            }
                                            icon={<Mail size={16}/>}
                                        />
                                        <InfoItem
                                            label="Company Phone"
                                            value={<PhoneNumberDisplay phoneNumber={companyData?.company_phone}
                                                                       className="font-medium"/>}
                                            icon={<Phone size={16}/>}
                                        />
                                    </div>
                                </TabsContent>

                                <TabsContent value="addresses" className="p-6 pt-4 space-y-8">
                                    <AddressDetails address={companyData?.registered_address}
                                                    title="Registered Address"/>
                                    {companyData?.operational_address && (
                                        <>
                                            <Separator/>
                                            <AddressDetails address={companyData?.operational_address}
                                                            title="Operational Address"/>
                                        </>
                                    )}
                                </TabsContent>

                                <TabsContent value="contacts" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="flex items-center justify-between">
                                            <h3 className="text-lg font-semibold">Company Contacts</h3>
                                            <Button size="sm" className="gap-2" onClick={handleAddContact}>
                                                <Plus size={14}/>
                                                Add Contact
                                            </Button>
                                        </div>
                                        {contacts.length > 0 ? (
                                            <div className="space-y-4">
                                                {contacts.map((contact) => (
                                                    <div key={contact._id}
                                                         className="flex items-start gap-4 p-4 rounded-lg border">
                                                        <Avatar className="h-12 w-12">
                                                            <AvatarFallback className="bg-teal-100 text-primary">
                                                                {contact.name?.charAt(0) || "C"}
                                                            </AvatarFallback>
                                                        </Avatar>
                                                        <div className="flex-1 space-y-1">
                                                            <div className="flex items-center justify-between">
                                                                <h4 className="font-medium">{contact.name || "N/A"}</h4>
                                                                <div className="flex items-center gap-2">
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        className="h-8 w-8"
                                                                        onClick={() => handleEditContact(contact)}
                                                                    >
                                                                        <Edit size={16}/>
                                                                    </Button>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                                                                        onClick={() => handleDeleteContact(contact._id)}
                                                                    >
                                                                        <Trash2 size={16}/>
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                            <p className="text-sm text-muted-foreground">{contact.role || "N/A"}</p>
                                                            <p className="text-sm text-muted-foreground">
                                                                {contact.contactType
                                                                    ? `${contact.contactType.charAt(0).toUpperCase() + contact.contactType.slice(1)} Contact`
                                                                    : "Contact"}
                                                            </p>
                                                            <div className="flex items-center gap-4 mt-2">
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <Mail size={14} className="text-muted-foreground"/>
                                                                    <a href={`mailto:${contact.email}`}
                                                                       className="text-primary hover:underline">
                                                                        {contact.email}
                                                                    </a>
                                                                </div>
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <Phone size={14} className="text-muted-foreground"/>
                                                                    <PhoneNumberDisplay phoneNumber={contact.phone}
                                                                                        className="font-medium"/>
                                                                </div>
                                                            </div>
                                                            {contact.notes && (
                                                                <p className="text-sm text-muted-foreground mt-2 italic">Note: {contact.notes}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div
                                                className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                                <Users size={48} className="text-muted-foreground mb-4"/>
                                                <h4 className="text-lg font-medium mb-2">No Contacts Added</h4>
                                                <p className="text-muted-foreground mb-4">This company doesn't have any
                                                    contacts added yet.</p>
                                                <Button size="sm" className="gap-2" onClick={handleAddContact}>
                                                    <Plus size={14}/>
                                                    Add First Contact
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>

                                <TabsContent value="products" className="p-6 pt-4">
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="text-lg font-semibold">Assigned Products</h3>
                                        {user.dashboard === "infinity" && userHasAssignProductPermission && (

                                            <Link href={`/lite/admin/companies/${params.id}/cip/create`} passHref>
                                                <Button size="sm" className="gap-2">
                                                    <Plus size={14}/>
                                                    Assign Product
                                                </Button>
                                            </Link>
                                        )}

                                    </div>
                                    {cip && cip.length > 0 ? (
                                        <div className="space-y-4">
                                            {cip.map((program, index) => (
                                                <div
                                                    key={program._id || index}
                                                    className="p-4 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
                                                    onClick={() => handleRowClick(program)}
                                                >
                                                    <div className="flex items-center justify-between">
                                                        <div className="space-y-1 flex-1">
                                                            <div className="flex items-start gap-2">
                                                                <div
                                                                    className="p-1.5 bg-primary/10 rounded text-primary mt-0.5">
                                                                    <Package size={16}/>
                                                                </div>
                                                                <div className="flex-1">
                                                                    {program.productVersionName && program.productVersionName.length > 0 ? (
                                                                        program.productVersionName.length === 1 ? (
                                                                            <h4 className="font-medium">
                                                                                {program.productVersionName[0]?.version_name || "Unnamed Product"} |{" "}
                                                                                {program.productVersionName[0]?.version_code}
                                                                            </h4>
                                                                        ) : (
                                                                            <div className="space-y-1">
                                                                                <h4 className="font-medium">
                                                                                    Multiple Product Versions
                                                                                    ({program.productVersionName.length})
                                                                                </h4>
                                                                                <div className="space-y-1">
                                                                                    {program.productVersionName.map((version, versionIndex) => (
                                                                                        <div key={versionIndex}
                                                                                             className="text-sm text-muted-foreground">
                                                                                            • {version?.version_name || "Unnamed Product"}
                                                                                        </div>
                                                                                    ))}
                                                                                </div>
                                                                            </div>
                                                                        )
                                                                    ) : (
                                                                        <h4 className="font-medium">Unnamed Product</h4>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {user.dashboard === "infinity"   && userHasAssignProductPermission && (
                                                            <div className="flex items-center gap-2">
                                                                <Link
                                                                    href={`/lite/admin/programmes/company/${params.id}/${program._id}/edit`}
                                                                    onClick={(e) => e.stopPropagation()}
                                                                >
                                                                    <Button variant="outline" size="sm">
                                                                        Edit
                                                                    </Button>
                                                                </Link>
                                                                <Button
                                                                    variant="destructive"
                                                                    size="sm"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation()
                                                                        handleUnassignProduct(program._id)
                                                                    }}
                                                                >
                                                                    Unassign
                                                                </Button>
                                                            </div>
                                                        )}

                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div
                                            className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                            <Package size={48} className="text-muted-foreground mb-4"/>
                                            <h4 className="text-lg font-medium mb-2">No Products Assigned</h4>
                                            <p className="text-muted-foreground mb-4">This company doesn't have any
                                                products assigned yet.</p>
                                            {user.dashboard === "infinity" && userHasAssignProductPermission && (
                                                <Link href={`/lite/admin/companies/${params.id}/cip/create`} passHref>
                                                    <Button size="sm" className="gap-2">
                                                        <Plus size={14}/>
                                                        Assign First Product
                                                    </Button>
                                                </Link>
                                            )}

                                        </div>
                                    )}
                                </TabsContent>

                                <TabsContent value="documents" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div>
                                            <h3 className="text-lg font-semibold mb-4">Application Documents</h3>
                                            <div className="grid gap-3 md:grid-cols-2">
                                                <DocumentItem name="Articles of Incorporation" type="PDF" size="2.1MB"/>
                                                <DocumentItem name="Company W-9" type="PDF" size="1.8MB"/>
                                                <DocumentItem name="EIN Letter" type="PDF" size="0.9MB"/>
                                                <DocumentItem name="Business License" type="PDF" size="1.2MB"/>
                                            </div>
                                        </div>
                                        <div>
                                            <div className="flex items-center justify-between mb-4">
                                                <h3 className="text-lg font-semibold">Additional Documents</h3>
                                                <Button variant="outline" size="sm" className="gap-2 bg-transparent">
                                                    <Plus size={14}/>
                                                    Upload Document
                                                </Button>
                                            </div>
                                            <div className="grid gap-3 md:grid-cols-2">
                                                {documents.map((doc, index) => (
                                                    <DocumentItem key={index} name={doc.name} type={doc.type}
                                                                  size={doc.size}/>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="questionnaire" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-3">What is your business type?</h4>
                                            <div className="space-y-2 pl-4">
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">10110 - Processing and
                                                        preserving of meat</p>
                                                </div>
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        10120 - Processing and preserving of poultry meat
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        10130 - Production of meat and poultry meat products
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-3">Please describe the products and/or
                                                services you offer:</h4>
                                            <p className="text-muted-foreground pl-4">
                                                Answer that was submitted goes here and can break unto multiple lines if
                                                it goes really far and
                                                the application typed out a lot.
                                            </p>
                                        </div>
                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">What is your company website URL?</h4>
                                                <p className="text-muted-foreground pl-4">https://google.com/shopping/</p>
                                            </div>
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">Choose the source of funds for your
                                                    company:</h4>
                                                <p className="text-muted-foreground pl-4">
                                                    Business income, Shareholder funds, Loan, Deposits & Savings
                                                </p>
                                            </div>
                                        </div>
                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">Choose the outbound payment types that
                                                    apply:</h4>
                                                <p className="text-muted-foreground pl-4">Other: "Text that user
                                                    inputted"</p>
                                            </div>
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected <span className="italic">inbound</span> monthly volume:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">Less than 100,000.00 EUR</p>
                                            </div>
                                        </div>
                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected number of <span className="italic">inbound</span> monthly
                                                    payments:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">Less than 100</p>
                                            </div>
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected countries of <span
                                                    className="italic">inbound</span> payments:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">United States, Argentina,
                                                    France</p>
                                            </div>
                                        </div>
                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-2">
                                                Expected <span className="italic">outbound</span> monthly volume:
                                            </h4>
                                            <p className="text-muted-foreground pl-4">100,000 - 500,000 EUR</p>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="signers" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="space-y-4">
                                            <h3 className="text-lg font-semibold">Beneficial Owners</h3>
                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger
                                                    className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback
                                                                className="bg-primary/10 text-primary">JS</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">John Smitherson</h4>
                                                            <p className="text-sm text-muted-foreground">35%
                                                                Ownership</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground"/>
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="John Smitherson"/>
                                                        <InfoItem label="Ownership" value="35%"/>
                                                        <InfoItem label="Email" value="<EMAIL>"/>
                                                        <InfoItem label="Phone" value="******-123-4567"/>
                                                        <InfoItem label="Date of Birth" value="15 Jan 1980"/>
                                                        <InfoItem label="Nationality" value="United States"/>
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>
                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger
                                                    className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback
                                                                className="bg-primary/10 text-primary">JD</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">Jane Doe</h4>
                                                            <p className="text-sm text-muted-foreground">25%
                                                                Ownership</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground"/>
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="Jane Doe"/>
                                                        <InfoItem label="Ownership" value="25%"/>
                                                        <InfoItem label="Email" value="<EMAIL>"/>
                                                        <InfoItem label="Phone" value="******-987-6543"/>
                                                        <InfoItem label="Date of Birth" value="22 Mar 1985"/>
                                                        <InfoItem label="Nationality" value="United States"/>
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>
                                        </div>
                                        <div className="space-y-4">
                                            <h3 className="text-lg font-semibold">Authorized Persons</h3>
                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger
                                                    className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback
                                                                className="bg-primary/10 text-primary">JS</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">Joseph Smith</h4>
                                                            <p className="text-sm text-muted-foreground">Authorized
                                                                Person</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground"/>
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="Joseph Smith"/>
                                                        <InfoItem label="Role" value="Authorized Person"/>
                                                        <InfoItem label="Email" value="<EMAIL>"/>
                                                        <InfoItem label="Phone" value="******-456-7890"/>
                                                        <InfoItem label="Date of Birth" value="10 Sep 1978"/>
                                                        <InfoItem label="Nationality" value="United States"/>
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="pmtypes" className="p-6 pt-4">
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold">Programme Manager Types</h3>
                                        {cip && cip.length > 0 ? (
                                            <div className="space-y-3">
                                                {cip.map((program) => (
                                                    <div key={program._id} className="p-4 rounded-lg border">
                                                        <div className="flex items-center gap-3">
                                                            <div className="p-1.5 bg-primary/10 rounded text-primary">
                                                                <Package size={16}/>
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium">{program.programManagerType?.manager_type || "N/A"}</h4>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div
                                                className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                                <Package size={48} className="text-muted-foreground mb-4"/>
                                                <h4 className="text-lg font-medium mb-2">No Programme Manager Types</h4>
                                                <p className="text-muted-foreground mb-4">
                                                    This company doesn't have any programme manager types assigned yet.
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>

                                <TabsContent value="summery" className="p-6 pt-4">
                                    {/* BIN Allocation Summary */}
                                    {cip && cip.length > 0 && (
                                        <div className="space-y-3">
                                            {cip.map((program, index) => (
                                                <BinAllocationSummary key={index} companyId={companyData._id}
                                                                      cipId={program._id}/>
                                            ))}{" "}
                                        </div>
                                    )}
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>

                 
                </div>
                {/* Sidebar */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Application Details</CardTitle>
                            <CardDescription>View application status and details</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-muted-foreground">Status</span>
                                    <StatusBadge status="Approved"/>
                                </div>

                                <Separator/>

                                <div className="space-y-3">
                                    <InfoItem
                                        label="Application ID"
                                        value={
                                            <div className="flex items-center gap-1">
                                                <span className="truncate max-w-[180px]">{companyData?.ryvyl_id}</span>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-6 w-6"
                                                    onClick={() => {
                                                        navigator.clipboard.writeText(companyData?.ryvyl_id)
                                                        alertHelper.showToast("ID copied to clipboard", "success")
                                                    }}
                                                >
                                                    <Copy size={14}/>
                                                </Button>
                                            </div>
                                        }
                                    />

                                    <InfoItem label="Origin" value="Onboarding Form"/>
                                    <InfoItem label="Programme Manager Name" value={companyData.company_name}/>

                                    <InfoItem label="Received Date" value={formatDateLocal(companyData?.createdAt)}/>

                                    {/*<InfoItem label="Due Date" value={formatDateLocal(companyData?.dueDate)}/>*/}

                                    {/*<InfoItem label="Approved Date" value={formatDateLocal(companyData?.approvedDate)}/>*/}

                                    {/*<InfoItem label="Application Approver" value={companyData?.approver || "N/A"}/>*/}

                                    {/*<InfoItem label="Assigned To" value={companyData?.assignedTo || "N/A"}/>*/}
                                </div>
                            </div>
                        </CardContent>
                    </Card>


                </div>
            </div>

            {/* Contact Form Dialog */}
            <ContactForm
                open={contactFormOpen}
                onOpenChange={setContactFormOpen}
                companyId={params.id}
                existingContact={selectedContact}
                onSuccess={refreshContacts}
            />
            <SendCredentialsDialog
                open={sendCredentialsOpen}
                onOpenChange={setSendCredentialsOpen}
                companyName={companyData?.company_name}
                companyEmail={companyData?.company_email}
                companyId={companyData?._id}
                contacts={contacts}
                previousPermissions={companyData?.permissions}
                previousPermissionAudit={companyData?.permissionsAudit}
                permissionsLog={companyData?.permissionsLog}
            />

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the contact from the company.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmDeleteContact} className="bg-red-600 hover:bg-red-700">
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}
