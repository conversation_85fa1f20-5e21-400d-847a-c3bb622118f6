"use client"

import type React from "react"

import { useInactivityTimer } from "@/hooks/use-inactivity-timer"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import axiosInstance, {removeTokenCookie} from "@/utils/axiosInstance";

interface AutoLogoutProviderProps {
    children: React.ReactNode
    timeoutMinutes?: number
    warningMinutes?: number
    onLogout?: () => void
}

export function AutoLogoutProvider({
                                       children,
                                       timeoutMinutes = 5,
                                       warningMinutes = 1,
                                       onLogout,
                                   }: AutoLogoutProviderProps) {
    const router = useRouter()
    const [showWarning, setShowWarning] = useState(false)
    const [countdown, setCountdown] = useState(0)

    const handleLogout = async () => {
        try {
            await axiosInstance.post("/auth/logout", {}, { withCredentials: true })
            removeTokenCookie()
        } catch (err) {
            console.error("Logout failed", err)
        }

        // Clear client state
        if (onLogout) onLogout()

        router.push("/login")
    }


    const handleTimeout = () => {
        setShowWarning(false)
        handleLogout()
    }

    const handleWarning = () => {
        setShowWarning(true)
        setCountdown(warningMinutes * 60) // Convert to seconds
    }

    const handleStayLoggedIn = () => {
        setShowWarning(false)
        setCountdown(0)
        // Timer will be reset automatically due to the click event
    }

    // Main inactivity timer
    useInactivityTimer({
        timeout: (timeoutMinutes - warningMinutes) * 60 * 1000, // Convert to milliseconds
        onTimeout: handleWarning,
    })

    // Warning countdown timer
    useEffect(() => {
        if (showWarning && countdown > 0) {
            const timer = setTimeout(() => {
                setCountdown((prev) => prev - 1)
            }, 1000)

            return () => clearTimeout(timer)
        } else if (showWarning && countdown === 0) {
            handleTimeout()
        }
    }, [showWarning, countdown])

    // Format countdown time
    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60)
        const secs = seconds % 60
        return `${mins}:${secs.toString().padStart(2, "0")}`
    }

    return (
        <>
            {children}

            <Dialog open={showWarning} onOpenChange={() => {}}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Session Timeout Warning</DialogTitle>
                        <DialogDescription>
                            Your session will expire in {formatTime(countdown)} due to inactivity. Click "Stay Logged In" to continue
                            your session.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex gap-2">
                        <Button variant="outline" onClick={handleLogout}>
                            Logout Now
                        </Button>
                        <Button onClick={handleStayLoggedIn}>Stay Logged In</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}
