//@ts-nocheck

import { type NextRequest, NextResponse } from "next/server"
import { connectToDatabase } from "@/lib/mongodb"
import type { Collection } from "mongodb"
import { cookies } from "next/headers"

// Helper function to decode JWT and get current user
const getCurrentUserId = async (request: NextRequest) => {
    try {
        

        // Check cookies first (now includes authToken)
        const cookieStore = await cookies()
        const cookieToken =
            cookieStore.get("authToken")?.value

        
        // Check Authorization header
        const authHeader = request.headers.get("authorization")
     

        let bearerToken = null
        if (authHeader?.startsWith("Bearer ")) {
            bearerToken = authHeader.substring(7)
           
        }

        // Use the token (prefer bearer token over cookie)
        const jwtToken = bearerToken || cookieToken

        if (!jwtToken) {
           
            return null
        }

         

        // Decode JWT
        const parts = jwtToken.split(".")
        if (parts.length !== 3) {
         
            return null
        }

        try {
            const payload = JSON.parse(atob(parts[1]))
        
            // Try multiple possible user ID fields
            const userId = payload.id || payload.sub || payload.userId || payload.user_id || payload.uid || payload._id
 

            if (!userId) {
                 return null
            }

            // Check if token is expired
            if (payload.exp && Date.now() >= payload.exp * 1000) {
                 return null
            }

         
            return String(userId) // Ensure it's a string
        } catch (decodeError) {
            console.error("JWT decode error:", decodeError)
            return null
        }
    } catch (error) {
        console.error("Error getting current user ID:", error)
        return null
    }
}

const saveActivityToDatabase = async (activityData: any) => {
    try {
        const db = await connectToDatabase()
        const collection: Collection = db.collection("activities")

        const activityWithTimestamp = {
            ...activityData,
            createdAt: new Date(),
            serverTimestamp: new Date().toISOString(),
        }

        await collection.insertOne(activityWithTimestamp)
    } catch (error) {
        console.error("Error saving activity:", error)
    }
}

export async function POST(request: NextRequest) {
    try {
        const activityData = await request.json()

        // Validate the incoming data
        if (!activityData.url || !activityData.pathname) {
            return NextResponse.json({ error: "Invalid activity data" }, { status: 400 })
        }

        // Ensure we have a user ID
        if (!activityData.user?.userId || !activityData.user?.isAuthenticated) {
            return NextResponse.json({ error: "Activity must be associated with an authenticated user" }, { status: 400 })
        }

        await saveActivityToDatabase(activityData)

        return NextResponse.json({ success: true, message: "Activity logged successfully" }, { status: 200 })
    } catch (error) {
        console.error("Error saving activity:", error)
        return NextResponse.json({ error: "Failed to log activity" }, { status: 500 })
    }
}

export async function GET(request: NextRequest) {
    try {
        const db = await connectToDatabase()
        const collection: Collection = db.collection("activities")

        const { searchParams } = new URL(request.url)

        // Get current user ID from JWT
        const currentUserId = await getCurrentUserId(request)

       

        if (!currentUserId) {
            // Add more specific error information
            const authHeader = request.headers.get("authorization")
            const hasAuthHeader = !!authHeader
            const cookieStore = await cookies()
            const cookieNames = ["authToken", "token", "jwt", "access_token", "auth-token"]
            const cookieValues = cookieNames.map((name) => ({
                name,
                exists: !!cookieStore.get(name)?.value,
                length: cookieStore.get(name)?.value?.length || 0,
            }))

           

            return NextResponse.json(
                {
                    error: "Unauthorized - No valid user session found",
                    debug: {
                        hasAuthHeader,
                        authHeaderType: authHeader ? (authHeader.startsWith("Bearer ") ? "Bearer" : "Other") : "None",
                        cookies: cookieValues,
                        message: "Check server logs for detailed JWT debugging information",
                    },
                },
                { status: 401 },
            )
        }

        // Pagination parameters
        const page = Number.parseInt(searchParams.get("page") || "1")
        const limit = Math.min(Number.parseInt(searchParams.get("limit") || "50"), 1000)
        const skip = (page - 1) * limit

        // Activity type filter
        const activityType = searchParams.get("type") // 'page_visits', 'api_actions', 'all'

        // Date range filters
        const startDate = searchParams.get("startDate")
        const endDate = searchParams.get("endDate")
        const last24h = searchParams.get("last24h") === "true"
        const last7d = searchParams.get("last7d") === "true"
        const last30d = searchParams.get("last30d") === "true"

        // Analytics parameters
        const analytics = searchParams.get("analytics") === "true"
        const groupBy = searchParams.get("groupBy")

        // Sort parameters - always sort by latest first
        const sortBy = "createdAt"
        const sortOrder = -1

        // Build filter query - ALWAYS filter by current user
        const filter: any = {
            "user.userId": currentUserId,
            "user.isAuthenticated": true,
        }

        // Filter by activity type
        if (activityType === "page_visits") {
            filter["context.isPageVisit"] = true
            filter.method = "GET"
        } else if (activityType === "api_actions") {
            filter["context.isUserAction"] = true
            filter["context.isApiRequest"] = true
        } else {
            // Default: show both page visits and API actions
            filter.$or = [{ "context.isPageVisit": true }, { "context.isUserAction": true, "context.isApiRequest": true }]
        }

        // Date filters
        if (startDate || endDate || last24h || last7d || last30d) {
            filter.createdAt = {}

            if (last24h) {
                filter.createdAt.$gte = new Date(Date.now() - 24 * 60 * 60 * 1000)
            } else if (last7d) {
                filter.createdAt.$gte = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            } else if (last30d) {
                filter.createdAt.$gte = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            } else {
                if (startDate) filter.createdAt.$gte = new Date(startDate)
                if (endDate) filter.createdAt.$lte = new Date(endDate)
            }
        }

        // If analytics is requested, return aggregated data
        if (analytics) {
            const analyticsData = await getAnalytics(collection, filter, groupBy)
            return NextResponse.json({
                success: true,
                analytics: analyticsData,
                filter: filter,
                timestamp: new Date().toISOString(),
            })
        }

        // Get total count for pagination
        const totalCount = await collection.countDocuments(filter)

        // Get activities with pagination and sorting (latest first)
        const activities = await collection
            .find(filter)
            .sort({ [sortBy]: sortOrder })
            .skip(skip)
            .limit(limit)
            .toArray()

        // Calculate pagination info
        const totalPages = Math.ceil(totalCount / limit)
        const hasNextPage = page < totalPages
        const hasPrevPage = page > 1

        return NextResponse.json({
            success: true,
            data: activities,
            pagination: {
                currentPage: page,
                totalPages,
                totalCount,
                limit,
                hasNextPage,
                hasPrevPage,
                nextPage: hasNextPage ? page + 1 : null,
                prevPage: hasPrevPage ? page - 1 : null,
            },
            filter: {
                userId: currentUserId,
                activityType: activityType || "all",
                sortOrder: "latest_first",
            },
            timestamp: new Date().toISOString(),
        })
    } catch (error) {
        console.error("Error retrieving activities:", error)
        return NextResponse.json(
            {
                error: "Failed to retrieve activities",
                details: error instanceof Error ? error.message : "Unknown error",
            },
            { status: 500 },
        )
    }
}

// Analytics helper function
async function getAnalytics(collection: Collection, baseFilter: any, groupBy?: string | null) {
    const analytics: any = {}

    // Basic stats
    const totalActivities = await collection.countDocuments(baseFilter)
    const pageVisits = await collection.countDocuments({ ...baseFilter, "context.isPageVisit": true })
    const apiActions = await collection.countDocuments({
        ...baseFilter,
        "context.isUserAction": true,
        "context.isApiRequest": true,
    })

    analytics.summary = {
        totalActivities,
        pageVisits,
        apiActions,
        mobileUsage: await collection.countDocuments({ ...baseFilter, "device.isMobile": true }),
    }

    // Top actions
    analytics.topActions = await collection
        .aggregate([
            { $match: { ...baseFilter, "context.isUserAction": true } },
            { $group: { _id: "$actionType", count: { $sum: 1 }, lastAction: { $max: "$createdAt" } } },
            { $sort: { count: -1 } },
            { $limit: 10 },
        ])
        .toArray()

    // Top pages
    analytics.topPages = await collection
        .aggregate([
            { $match: { ...baseFilter, "context.isPageVisit": true } },
            { $group: { _id: "$pathname", count: { $sum: 1 }, lastVisit: { $max: "$createdAt" } } },
            { $sort: { count: -1 } },
            { $limit: 10 },
        ])
        .toArray()

    return analytics
}
