//@ts-nocheck
import { type NextRequest, NextResponse } from "next/server"
import { saveRecordsToMongoDB, saveFileMetadataToMongoDB } from "@/lib/mongodb"

function validateRecord(record) {
  if (typeof record !== "object" || record === null) return "Each record must be an object"
  if (typeof record.fileName !== "string" || !record.fileName.trim()) return "Each record must have a non-empty fileName string"
  if (typeof record.sheetName !== "string" || !record.sheetName.trim()) return "Each record must have a non-empty sheetName string"
  if (!Array.isArray(record.headers)) return "Each record must have a headers array"
  if (!Array.isArray(record.rowData)) return "Each record must have a rowData array"
  if (typeof record.rowIndex !== "number" || !Number.isFinite(record.rowIndex)) return "Each record must have a numeric rowIndex"
  if (typeof record.totalRows !== "number" || !Number.isFinite(record.totalRows)) return "Each record must have a numeric totalRows"
  return null
}

function validateMetadata(metadata) {
  if (typeof metadata !== "object" || metadata === null) return "Metadata must be an object"
  // Optionally add more checks for required metadata fields here
  return null
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { records, metadata } = body

    if (!records || !Array.isArray(records) || records.length === 0) {
      return NextResponse.json({ error: "'records' must be a non-empty array" }, { status: 400 })
    }

    for (let i = 0; i < records.length; i++) {
      const error = validateRecord(records[i])
      if (error) {
        return NextResponse.json({ error: `Record at index ${i}: ${error}` }, { status: 400 })
      }
    }

    if (metadata !== undefined && metadata !== null) {
      const metaError = validateMetadata(metadata)
      if (metaError) {
        return NextResponse.json({ error: metaError }, { status: 400 })
      }
    }

    // Save file metadata first
    let metadataId = null
    if (metadata) {
      metadataId = await saveFileMetadataToMongoDB(metadata)
    }

    // Transform records to the new format
    const transformedRecords = records.map((record: any) => ({
      fileName: record.fileName,
      sheetName: record.sheetName,
      headers: record.headers,
      rowData: record.rowData,
      rowIndex: record.rowIndex,
      totalRows: record.totalRows,
    }))

    // Save records with progress tracking
    const savedIds = await saveRecordsToMongoDB(transformedRecords, () => {})

    return NextResponse.json({
      success: true,
      savedCount: savedIds.length,
      metadataId,
      message: `Successfully saved ${savedIds.length} records to PocztaPost collection`,
    })
  } catch (error) {
    console.error("Error in save-records API:", error)
    return NextResponse.json({ error: `Failed to save records: ${error}` }, { status: 500 })
  }
}
