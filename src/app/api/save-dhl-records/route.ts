//@ts-nocheck
import { type NextRequest, NextResponse } from "next/server"
import { saveFileMetadataToMongoDB } from "@/lib/mongodb"
import type { DuplicateDetectionConfig } from "@/lib/duplicate-detection"
import type { DatabaseComparisonResult } from "@/lib/database-comparison"
import { connectToDatabase, saveDHLRecordsToMongoDB } from "@/lib/mongodb"

interface EnhancedUploadOptions {
  duplicateDetection: DuplicateDetectionConfig
  compareWithDatabase: boolean
}

// Server-side duplicate detection
function generateKey(record: Record<string, any>, keyFields: string[]): string {
  return keyFields
    .map((field) =>
      String(record[field] || "")
        .toLowerCase()
        .trim(),
    )
    .join("|")
}

async function checkDuplicateInDatabase(
  record: Record<string, any>,
  keyFields: string[],
): Promise<{ isDuplicate: boolean; existingRecord?: any }> {
  try {
    const database = await connectToDatabase()
    const collection = database.collection("DHLDeliveryMethods")

    // Create filter based only on the key fields (Country)
    const filter: any = {}
    keyFields.forEach((field) => {
      filter[field] = record[field]
    })

    const existingRecord = await collection.findOne(filter)

    return {
      isDuplicate: !!existingRecord,
      existingRecord,
    }
  } catch (error) {
    console.error("Error checking duplicate in database:", error)
    return { isDuplicate: false }
  }
}

function getChangedFields(existingRecord: any, newRecord: any): string[] {
  const changedFields: string[] = []

  // Get all fields from both records (excluding metadata fields)
  const allFields = new Set([
    ...Object.keys(existingRecord).filter((key) => !key.startsWith("_")),
    ...Object.keys(newRecord).filter((key) => !key.startsWith("_")),
  ])

  allFields.forEach((field) => {
    const existingValue = existingRecord[field]
    const newValue = newRecord[field]

    // Normalize values for comparison
    const normalizedExisting = normalizeValue(existingValue)
    const normalizedNew = normalizeValue(newValue)

    if (normalizedExisting !== normalizedNew) {
      changedFields.push(field)
    }
  })

  return changedFields
}

function normalizeValue(value: any): string {
  if (value === null || value === undefined) return ""
  if (typeof value === "string") return value.trim()
  return String(value)
}

async function compareWithDatabase(
  newRecords: Record<string, any>[],
  duplicateConfig: { primaryKey: string[] },
): Promise<DatabaseComparisonResult> {
  try {
    // Fetch ALL existing records from database (not filtered by filename)
    const database = await connectToDatabase()
    const collection = database.collection("DHLDeliveryMethods")
    const existingRecords = await collection.find({}).toArray()

    const changes: Array<{
      type: "new" | "updated" | "duplicate"
      record: Record<string, any>
      previousRecord?: Record<string, any>
      changedFields?: string[]
    }> = []

    const fieldChanges: Record<
      string,
      {
        added: number
        modified: number
        removed: number
      }
    > = {}

    let newRecords_count = 0
    let updatedRecords = 0
    let duplicatesFound = 0

    // Create a map of existing records for quick lookup
    const existingRecordsMap = new Map<string, any>()
    existingRecords.forEach((record) => {
      const key = generateKey(record, duplicateConfig.primaryKey)
      existingRecordsMap.set(key, record)
    })

    // Process each new record
    for (const newRecord of newRecords) {
      const key = generateKey(newRecord, duplicateConfig.primaryKey)
      const existingRecord = existingRecordsMap.get(key)

      if (existingRecord) {
        // Record exists, check for changes
        const changedFields = getChangedFields(existingRecord, newRecord)

        if (changedFields.length > 0) {
          // Record has changes
          updatedRecords++
          changes.push({
            type: "updated",
            record: newRecord,
            previousRecord: existingRecord,
            changedFields,
          })

          // Track field-level changes
          changedFields.forEach((field) => {
            if (!fieldChanges[field]) {
              fieldChanges[field] = { added: 0, modified: 0, removed: 0 }
            }
            fieldChanges[field].modified++
          })
        } else {
          // Exact duplicate
          duplicatesFound++
          changes.push({
            type: "duplicate",
            record: newRecord,
            previousRecord: existingRecord,
          })
        }
      } else {
        // New record
        newRecords_count++
        changes.push({
          type: "new",
          record: newRecord,
        })

        // Track new fields
        Object.keys(newRecord).forEach((field) => {
          if (!field.startsWith("_")) {
            // Skip metadata fields
            if (!fieldChanges[field]) {
              fieldChanges[field] = { added: 0, modified: 0, removed: 0 }
            }
            fieldChanges[field].added++
          }
        })
      }
    }

    // Generate summary
    let summary = `Database comparison completed. `
    if (newRecords_count > 0) summary += `${newRecords_count} new countries. `
    if (updatedRecords > 0) summary += `${updatedRecords} countries updated. `
    if (duplicatesFound > 0) summary += `${duplicatesFound} exact duplicates found. `
    if (summary === "Database comparison completed. ") summary += "No changes detected."

    return {
      summary,
      newRecords: newRecords_count,
      updatedRecords,
      duplicatesFound,
      totalProcessed: newRecords.length,
      changes: changes.slice(0, 100), // Limit to first 100 changes for performance
      fieldChanges,
    }
  } catch (error) {
    console.error("Error comparing with database:", error)
    throw new Error(`Database comparison failed: ${error}`)
  }
}

// DHL record validation
function validateDHLRowData(rowData: Record<string, any>): string | null {
  if (!rowData || typeof rowData !== "object") return "rowData must be an object"
  if (typeof rowData.country !== "string" || !rowData.country.trim()) return "country is required and must be a non-empty string"
  if (typeof rowData.deliveryMethod !== "string" || !rowData.deliveryMethod.trim()) return "deliveryMethod is required and must be a non-empty string"
  if (typeof rowData.cost !== "number" || isNaN(rowData.cost) || rowData.cost < 0) return "cost is required and must be a non-negative number"
  if (typeof rowData.estimatedDays !== "number" || isNaN(rowData.estimatedDays) || rowData.estimatedDays < 0) return "estimatedDays is required and must be a non-negative number"
  // Add more field checks as needed
  return null
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { records, options, isFirstBatch } = body as {
      records: Array<{
        fileName: string
        sheetName: string
        headers: string[]
        rowData: Record<string, any>
        rowIndex: number
        totalRows: number
      }>
      options: EnhancedUploadOptions
      isFirstBatch: boolean
    }

    if (!records || !Array.isArray(records)) {
      return NextResponse.json({ error: "Invalid records data" }, { status: 400 })
    }

    // Validate all records before processing
    for (let i = 0; i < records.length; i++) {
      const error = validateDHLRowData(records[i].rowData)
      if (error) {
        return NextResponse.json({ error: `Record at index ${i}: ${error}` }, { status: 400 })
      }
    }

    let comparisonResult: DatabaseComparisonResult | undefined

    // Compare with database if requested and this is the first batch
    if (options.compareWithDatabase && isFirstBatch && records.length > 0) {
      try {
        const recordData = records.map((r) => r.rowData)
        comparisonResult = await compareWithDatabase(recordData, {
          primaryKey: options.duplicateDetection.primaryKey,
        })
      } catch (error) {
        console.error("Database comparison failed:", error)
      }
    }

    // Process records with strict duplicate detection
    const processedRecords: Array<{
      fileName: string
      sheetName: string
      headers: string[]
      rowData: Record<string, any>
      rowIndex: number
      totalRows: number
    }> = []

    const duplicateDetails: Array<{
      newRecord: Record<string, any>
      existingRecord: Record<string, any>
      action: "replaced" | "skipped"
    }> = []

    let duplicateCount = 0
    let replacedCount = 0

    for (const record of records) {
      // Check for duplicates in the entire database (not just current file)
      const duplicateResult = await checkDuplicateInDatabase(record.rowData, options.duplicateDetection.primaryKey)

      if (duplicateResult.isDuplicate) {
        duplicateCount++

        if (options.duplicateDetection.strategy === "replace") {
          // Update the existing record instead of creating new one
          try {
            const database = await connectToDatabase()
            const collection = database.collection("DHLDeliveryMethods")

            // Create filter to find the existing record based only on Country
            const filter: any = {}
            options.duplicateDetection.primaryKey.forEach((key) => {
              filter[key] = record.rowData[key]
            })

            // Update the existing record with new data
            const updateData = {
              ...record.rowData,
              _fileName: record.fileName,
              _sheetName: record.sheetName,
              _rowIndex: record.rowIndex,
              _totalRows: record.totalRows,
              _updatedAt: new Date(),
            }

            const updateResult = await collection.updateOne(filter, { $set: updateData })

            if (updateResult.modifiedCount > 0) {
              replacedCount++
              duplicateDetails.push({
                newRecord: record.rowData,
                existingRecord: duplicateResult.existingRecord!,
                action: "replaced",
              })
            }
          } catch (error) {
            console.error("Error updating duplicate record:", error)
            // If update fails, skip this record
          }
        } else if (options.duplicateDetection.strategy === "skip") {
          // Skip the duplicate record
          duplicateDetails.push({
            newRecord: record.rowData,
            existingRecord: duplicateResult.existingRecord!,
            action: "skipped",
          })
        }
      } else {
        // Not a duplicate, add to processed records for insertion
        processedRecords.push(record)
      }
    }

    // Save file metadata if this is the first batch
    let metadataId = null
    if (isFirstBatch && (processedRecords.length > 0 || replacedCount > 0)) {
      const firstRecord = processedRecords[0] || records[0]
      metadataId = await saveFileMetadataToMongoDB({
        fileName: firstRecord.fileName,
        sheetName: firstRecord.sheetName,
        totalRows: firstRecord.totalRows,
        totalColumns: firstRecord.headers.length,
        headers: firstRecord.headers,
        uploadedAt: new Date(),
        createdAt: new Date(),
      })
    }

    // Save processed records to database using DHL-specific function
    const savedIds = await saveDHLRecordsToMongoDB(processedRecords, () => {})

    return NextResponse.json({
      success: true,
      savedRecords: processedRecords.length,
      duplicatesHandled: duplicateCount,
      replacedRecords: replacedCount,
      comparisonResult,
      duplicateDetails: duplicateDetails.slice(0, 50), // Limit for performance
      metadataId,
      message: `Successfully processed ${processedRecords.length} new countries and updated ${replacedCount} existing countries`,
    })
  } catch (error) {
    console.error("Error in enhanced save-records API:", error)
    return NextResponse.json({ error: `Failed to save records: ${error}` }, { status: 500 })
  }
}
