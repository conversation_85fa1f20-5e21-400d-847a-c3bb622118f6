//@ts-nocheck
"use client"

import { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react"
import { useAppSelector } from "@/store/hooks"
import axiosInstance from "@/utils/axiosInstance"
import { CardDetailsSheet } from "@/app/lite/admin/individual/v1/[id]/card"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertCircle, CreditCard } from "lucide-react"
import DataExporter from "@/components/DataExporter"
import { useSearchParams } from "next/navigation"
import Image from "next/image"

const asset = process.env.NEXT_PUBLIC_ASSET_URL;
const NEXT_PUBLIC_DEFAULT_COMPANY_ID = process.env.NEXT_PUBLIC_DEFAULT_COMPANY_ID;
const NEXT_PUBLIC_PHYSICAL_CARD_LIMIT = Number(process.env.NEXT_PUBLIC_PHYSICAL_CARD_LIMIT);
const NEXT_PUBLIC_VIRTUAL_CARD_LIMIT = Number(process.env.NEXT_PUBLIC_VIRTUAL_CARD_LIMIT);

interface CardImage {
    _id: string
    front_side: string
    back_side: string
    product_version: {
        version_code: string
    }
    company: {
        _id: string
    }
}

export default function CardManagement() {
    const searchParams = useSearchParams()
    const [isSheetOpen, setIsSheetOpen] = useState(false)
    const [selectedCardId, setSelectedCardId] = useState<string | null>(null)
    const user = useAppSelector((state) => state.user.user)
    const [onboarding, setOnboarding] = useState<null | any>(null)
    const [account, setAccount] = useState<null | any>(null)
    const [cards, setCards] = useState<null | any>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [currentCardImages, setCurrentCardImages] = useState<{ [key: string]: CardImage }>({})
    const [cardImagesLoading, setCardImagesLoading] = useState(false)

    // Memoize card categorization
    const { physicalCards, virtualCards, activeCards, inactiveCards } = useMemo(() => {
        if (!cards || cards.length === 0) {
            return {
                physicalCards: [],
                virtualCards: [],
                activeCards: [],
                inactiveCards: [],
            }
        }

        const physical = cards.filter((card) => card.productDesc && card.productDesc.includes("PHY"))
        const virtual = cards.filter((card) => !card.productDesc || !card.productDesc.includes("PHY"))
        const active = cards.filter((c) => c.status.toUpperCase() === "ACTIVE")
        const inactive = cards.filter((c) => c.status.toUpperCase() !== "ACTIVE")

        return {
            physicalCards: physical,
            virtualCards: virtual,
            activeCards: active,
            inactiveCards: inactive,
        }
    }, [cards])

    // Memoize card data for export
    const cardsData = useMemo(() => {
        if (!cards || cards.length === 0) return []

        return cards.map((row, index) => ({
            id: index + 1,
            emboss_name1: row.embossName1,
            card_number: row.cardMask,
            exp_date: row.expDate,
            status: row.status,
            created_at: formatDate(row.createdAt),
            type: row.productDesc && row.productDesc.includes("PHY") ? "Physical" : "Virtual",
        }))
    }, [cards])

    // Check for card ID in URL when component mounts or URL changes
    useEffect(() => {
        const cardId = searchParams.get("card")
        if (cardId) {
            setSelectedCardId(cardId)
            setIsSheetOpen(true)
        }
    }, [searchParams])

    // Update URL when sheet is opened/closed
    useEffect(() => {
        if (isSheetOpen && selectedCardId) {
            const url = new URL(window.location.href)
            url.searchParams.set("card", selectedCardId)
            window.history.pushState({}, "", url.toString())
        } else {
            if (searchParams.has("card")) {
                const url = new URL(window.location.href)
                url.searchParams.delete("card")
                window.history.pushState({}, "", url.toString())
            }
        }
    }, [isSheetOpen, selectedCardId, searchParams])

    // Fetch all data in parallel for better performance
    const fetchAllData = useCallback(async () => {
        try {
            setLoading(true)
            setError(null)

            if (!user.recordId) {
                throw new Error("User record ID not found")
            }

            // Fetch onboarding details and card images in parallel
            const [onboardingResponse, cardImagesResponse] = await Promise.all([
                axiosInstance.get(`onboarding/personal/${user.recordId}`),
                axiosInstance.get<CardImage[]>("/images"),
            ])

            const onboardingData = onboardingResponse.data.data
            const accountData = onboardingResponse.data.account[0]
            const cardsData = onboardingResponse.data.cards || []

            setOnboarding(onboardingData)
            setAccount(accountData)
            setCards(cardsData)
            setCardImages(cardImagesResponse.data)

            // Process card images
            if (cardsData.length > 0 && cardImagesResponse.data) {
                processCardImages(cardsData, cardImagesResponse.data, onboardingData?.company?._id)
            }


            if (user?.cardholder.userType === "b2b") {
                try {

                    const b2bcards = await axiosInstance.get(`b2b/cardholder/${user.recordId}/cards`)
                    console.log("B2B Cards Full Response:", b2bcards)
                    console.log("B2B Cards Data:", b2bcards.data)

                    // Handle different possible response structures
                    let b2bCardsData = null
                    if (b2bcards.data) {
                        if (Array.isArray(b2bcards.data)) {
                            b2bCardsData = b2bcards.data
                        } else if (b2bcards.data.data && Array.isArray(b2bcards.data.data)) {
                            b2bCardsData = b2bcards.data.data
                        } else if (b2bcards.data.cards && Array.isArray(b2bcards.data.cards)) {
                            b2bCardsData = b2bcards.data.cards
                        } else if (b2bcards.data.result && Array.isArray(b2bcards.data.result)) {
                            b2bCardsData = b2bcards.data.result
                        }
                    }

                    console.log("Processed B2B Cards Data:", b2bCardsData)
                    console.log("B2B Cards Count:", b2bCardsData?.length || 0)

                    if (b2bCardsData && Array.isArray(b2bCardsData)) {
                        setCards(b2bCardsData)

                        // Process card images for B2B cards if they exist
                        if (b2bCardsData.length > 0 && cardImagesResponse.data) {
                            processCardImages(b2bCardsData, cardImagesResponse.data, onboardingData?.company?._id)
                        }
                    } else {
                        console.warn("No valid B2B cards data found")
                        setCards([])
                    }
                } catch (b2bError) {
                    console.error("Error fetching B2B cards:", b2bError)
                    console.error("B2B Error details:", {
                        message: b2bError.message,
                        response: b2bError.response?.data,
                        status: b2bError.response?.status,
                    })
                    // Fallback to regular cards if B2B fetch fails
                    setCards(cardsData || [])
                }
            } else {
                console.log("Using regular cards data")
                setCards(cardsData || [])
            }
        } catch (error: any) {
            console.error("Error fetching data:", error)
            setError(error.message)
        } finally {
            setLoading(false)
        }
    }, [user.recordId])

    // Process card images once we have both cards and images
    const processCardImages = useCallback((cards, images, companyId) => {
        setCardImagesLoading(true)
        try {
            const imageMap: { [key: string]: CardImage } = {}

            cards.forEach((card) => {
                const matchingImages = images.filter((img) => img.product_version.version_code === card.productCode)
                let companyImage = matchingImages.find((img) => img.company._id === companyId)

                if (!companyImage) {
                    companyImage = matchingImages.find((img) => img.company._id === NEXT_PUBLIC_DEFAULT_COMPANY_ID)
                }

                if (companyImage) {
                    imageMap[card.cardKey] = companyImage
                }
            })

            setCurrentCardImages(imageMap)
        } catch (error) {
            console.error("Error processing card images:", error)
        } finally {
            setCardImagesLoading(false)
        }
    }, [])

    useEffect(() => {
        fetchAllData()
    }, [fetchAllData])

    const handleCardClick = useCallback((cardId: string) => {
        setSelectedCardId(cardId)
        setIsSheetOpen(true)
    }, [])

    const handleSheetOpenChange = useCallback((open: boolean) => {
        setIsSheetOpen(open)
        if (!open) {
            const url = new URL(window.location.href)
            url.searchParams.delete("card")
            window.history.pushState({}, "", url.toString())
        }
    }, [])

    if (loading) {
        return <LoadingOverlay />
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="flex items-center gap-2 mb-4">
                    <AlertCircle className="h-6 w-6 text-red-500" />
                    <h2 className="text-xl font-semibold text-red-500">Error Loading Data</h2>
                </div>
                <p className="text-red-500 mb-4">Error loading card data: {error}</p>
                <button onClick={fetchAllData} className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Retry
                </button>
            </div>
        )
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold mb-6">Card Management</h1>

            {account && (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
                    <Card>
                        <CardHeader>
                            <CardTitle>Total Cards</CardTitle>
                            <CardDescription>Number of cards associated with your account</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold">{cards?.length || 0}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader>
                            <CardTitle>Active Cards</CardTitle>
                            <CardDescription>Number of currently active cards</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-green-600">{activeCards.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader>
                            <CardTitle>Physical Cards</CardTitle>
                            <CardDescription>
                                Physical cards ({physicalCards.length}/{NEXT_PUBLIC_PHYSICAL_CARD_LIMIT})
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-blue-600">{physicalCards.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader>
                            <CardTitle>Virtual Cards</CardTitle>
                            <CardDescription>
                                Virtual cards ({virtualCards.length}/{NEXT_PUBLIC_VIRTUAL_CARD_LIMIT})
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-purple-600">{virtualCards.length}</div>
                        </CardContent>
                    </Card>
                </div>
            )}

            <Tabs defaultValue="all" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="all">All Cards ({cards?.length || 0})</TabsTrigger>
                    <TabsTrigger value="active">Active ({activeCards.length})</TabsTrigger>
                    <TabsTrigger value="inactive">Inactive ({inactiveCards.length})</TabsTrigger>
                    <TabsTrigger value="physical">Physical ({physicalCards.length})</TabsTrigger>
                    <TabsTrigger value="virtual">Virtual ({virtualCards.length})</TabsTrigger>
                </TabsList>

                <TabsContent value="all">
                    <CardList
                        cards={cards || []}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="All Cards"
                        description="All cards associated with your account"
                        exportData={cardsData}
                    />
                </TabsContent>

                <TabsContent value="active">
                    <CardList
                        cards={activeCards}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="Active Cards"
                        description="Currently active cards"
                        exportData={cardsData.filter((card) => card.status.toUpperCase() === "ACTIVE")}
                    />
                </TabsContent>

                <TabsContent value="inactive">
                    <CardList
                        cards={inactiveCards}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="Inactive Cards"
                        description="Cards that are not currently active"
                        exportData={cardsData.filter((card) => card.status.toUpperCase() !== "ACTIVE")}
                    />
                </TabsContent>

                <TabsContent value="physical">
                    <CardList
                        cards={physicalCards}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="Physical Cards"
                        description={`Physical cards (${physicalCards.length}/${NEXT_PUBLIC_PHYSICAL_CARD_LIMIT} limit)`}
                        exportData={cardsData.filter((card) => card.type === "Physical")}
                    />
                </TabsContent>

                <TabsContent value="virtual">
                    <CardList
                        cards={virtualCards}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="Virtual Cards"
                        description={`Virtual cards (${virtualCards.length}/${NEXT_PUBLIC_VIRTUAL_CARD_LIMIT} limit)`}
                        exportData={cardsData.filter((card) => card.type === "Virtual")}
                    />
                </TabsContent>
            </Tabs>

            {onboarding && (
                <CardDetailsSheet
                    companyId={onboarding?.company?._id}
                    isOpen={isSheetOpen}
                    onOpenChange={handleSheetOpenChange}
                    cardId={selectedCardId || ""}
                />
            )}
        </div>
    )
}

interface CardListProps {
    cards: any[]
    onCardClick: (cardId: string) => void
    currentCardImages: { [key: string]: CardImage }
    cardImagesLoading: boolean
    title: string
    description: string
    exportData: any[]
}

// Utility function
function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    })
}

function getStatusColor(status: string) {
    switch (status.toUpperCase()) {
        case "ACTIVE":
            return "bg-green-500"
        case "INACTIVE":
            return "bg-yellow-500"
        case "BLOCKED":
            return "bg-red-500"
        case "ORDERED":
            return "bg-blue-500"
        default:
            return "bg-gray-500"
    }
}

function CardList({
                      cards,
                      onCardClick,
                      currentCardImages,
                      cardImagesLoading,
                      title,
                      description,
                      exportData,
                  }: CardListProps) {
    // Render card image with proper loading and fallback
    const renderCardImage = (cardKey: string) => {
        if (cardImagesLoading) {
            return <Skeleton className="h-[25px] w-[40px] rounded" />
        }

        const cardImage = currentCardImages[cardKey]
        const imageSrc = cardImage ? `${asset}/${cardImage.front_side}` : "/cards/pp-fd.svg"

        return (
            <Image
                src={imageSrc || "/cards/pp-fd.svg"}
                alt="Credit card"
                width={40}
                height={25}
                className="object-cover"
                priority={true}
                unoptimized
                onError={(e) => {
                    // Fallback to default image on error
                    e.currentTarget.src = "/cards/pp-fd.svg"
                }}
            />
        )
    }

    if (!cards || cards.length === 0) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>{title}</CardTitle>
                    <CardDescription>{description}</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="bg-muted/20 p-8 rounded-lg text-center">
                        <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h4 className="text-lg font-medium mb-2">No Cards Found</h4>
                        <p className="text-muted-foreground max-w-md mx-auto">No cards found in this category.</p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>
                    {title} ({cards.length})
                </CardTitle>
                <CardDescription>{description}</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="mb-4">
                    <DataExporter
                        data={exportData}
                        filename={`${title.toLowerCase().replace(/\s+/g, "_")}_cards`}
                        title={`${title} Report`}
                    />
                </div>
                <div className="border rounded-lg">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Card</TableHead>
                                <TableHead>Name</TableHead>
                                <TableHead>Number</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Type</TableHead>
                                <TableHead>Expiry</TableHead>
                                <TableHead>Created</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {cards.map((card, index) => (
                                <TableRow
                                    onClick={() => onCardClick(card.cardKey)}
                                    key={index}
                                    className="cursor-pointer hover:bg-muted/50"
                                >
                                    <TableCell>{renderCardImage(card.cardKey)}</TableCell>
                                    <TableCell className="font-medium">{card.nickName ? card.nickName : card.embossName1}</TableCell>
                                    <TableCell className="font-mono">•••• {card.cardMask.slice(-4)}</TableCell>
                                    <TableCell>
                                        <Badge className={getStatusColor(card.status)}>{card.status}</Badge>
                                    </TableCell>
                                    <TableCell>
                                        <Badge variant="outline">
                                            {card.productDesc && card.productDesc.includes("PHY") ? "Physical" : "Virtual"}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>{card.expDate}</TableCell>
                                    <TableCell>{formatDate(card.createdAt)}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
    )
}
