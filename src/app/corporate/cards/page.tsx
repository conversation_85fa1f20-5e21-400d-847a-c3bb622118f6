//@ts-nocheck
"use client"

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react"
import { useAppSelector } from "@/store/hooks"
import axiosInstance from "@/utils/axiosInstance"
import { CardDetailsSheet } from "@/app/lite/admin/individual/v1/[id]/card"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { AlertCircle, CreditCard, RefreshCw, Wifi, WifiOff, <PERSON>, Clock } from "lucide-react"
import DataExporter from "@/components/DataExporter"
import { useSearchParams } from "next/navigation"
import Image from "next/image"

const asset = process.env.NEXT_PUBLIC_ASSET_URL;
const NEXT_PUBLIC_DEFAULT_COMPANY_ID = process.env.NEXT_PUBLIC_DEFAULT_COMPANY_ID;
const NEXT_PUBLIC_PHYSICAL_CARD_LIMIT = process.env.NEXT_PUBLIC_PHYSICAL_CARD_LIMIT;
const NEXT_PUBLIC_VIRTUAL_CARD_LIMIT = process.env.NEXT_PUBLIC_VIRTUAL_CARD_LIMIT;

interface CardImage {
    _id: string
    front_side: string
    back_side: string
    product_version: {
        version_code: string
    }
    company: {
        _id: string
    }
}

interface ErrorState {
    type: "network" | "auth" | "timeout" | "server" | "permission" | "general"
    message: string
    code?: string | number
    retryable: boolean
}

export default function CardManagement() {
    const searchParams = useSearchParams()
    const [isSheetOpen, setIsSheetOpen] = useState(false)
    const [selectedCardId, setSelectedCardId] = useState<string | null>(null)
    const user = useAppSelector((state) => state.user.user)
    const [onboarding, setOnboarding] = useState<null | any>(null)
    const [account, setAccount] = useState<null | any>(null)
    const [cards, setCards] = useState<null | any>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<ErrorState | null>(null)
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [currentCardImages, setCurrentCardImages] = useState<{ [key: string]: CardImage }>({})
    const [cardImagesLoading, setCardImagesLoading] = useState(false)
    const [companyData, setCompanyData] = useState<any>(null)
    const [retryCount, setRetryCount] = useState(0)

    // Memoize card categorization
    const { physicalCards, virtualCards, activeCards, inactiveCards } = useMemo(() => {
        if (!cards || cards.length === 0) {
            return {
                physicalCards: [],
                virtualCards: [],
                activeCards: [],
                inactiveCards: [],
            }
        }

        const physical = cards.filter((card) => card.productDesc && card.productDesc.includes("PHY"))
        const virtual = cards.filter((card) => !card.productDesc || !card.productDesc.includes("PHY"))
        const active = cards.filter((c) => c.status.toUpperCase() === "ACTIVE")
        const inactive = cards.filter((c) => c.status.toUpperCase() !== "ACTIVE")

        return {
            physicalCards: physical,
            virtualCards: virtual,
            activeCards: active,
            inactiveCards: inactive,
        }
    }, [cards])

    // Memoize card data for export
    const cardsData = useMemo(() => {
        if (!cards || cards.length === 0) return []

        return cards.map((row, index) => ({
            id: index + 1,
            emboss_name1: row.embossName1,
            card_number: row.cardMask,
            exp_date: row.expDate,
            status: row.status,
            created_at: formatDate(row.createdAt),
            type: row.productDesc && row.productDesc.includes("PHY") ? "Physical" : "Virtual",
        }))
    }, [cards])

    // Enhanced error handling function
    const handleError = useCallback((error: any): ErrorState => {
        console.error("Error details:", error)

        // Network errors
        if (!navigator.onLine) {
            return {
                type: "network",
                message: "No internet connection. Please check your network and try again.",
                retryable: true,
            }
        }

        // Axios/HTTP errors
        if (error.response) {
            const status = error.response.status
            const data = error.response.data

            switch (status) {
                case 401:
                    return {
                        type: "auth",
                        message: "Your session has expired. Please log in again.",
                        code: status,
                        retryable: false,
                    }
                case 403:
                    return {
                        type: "permission",
                        message: "You don't have permission to access card data.",
                        code: status,
                        retryable: false,
                    }
                case 404:
                    return {
                        type: "general",
                        message: "Card data not found. This might be a new account.",
                        code: status,
                        retryable: true,
                    }
                case 429:
                    return {
                        type: "server",
                        message: "Too many requests. Please wait a moment before trying again.",
                        code: status,
                        retryable: true,
                    }
                case 500:
                case 502:
                case 503:
                case 504:
                    return {
                        type: "server",
                        message: "Server is temporarily unavailable. Please try again in a few minutes.",
                        code: status,
                        retryable: true,
                    }
                default:
                    return {
                        type: "server",
                        message: data?.message || `Server error (${status}). Please try again.`,
                        code: status,
                        retryable: true,
                    }
            }
        }

        // Request timeout
        if (error.code === "ECONNABORTED" || error.message?.includes("timeout")) {
            return {
                type: "timeout",
                message: "Request timed out. Please check your connection and try again.",
                retryable: true,
            }
        }

        // Network error (no response)
        if (error.request) {
            return {
                type: "network",
                message: "Unable to connect to the server. Please check your internet connection.",
                retryable: true,
            }
        }

        // User record ID missing
        if (error.message?.includes("User record ID")) {
            return {
                type: "auth",
                message: "User authentication error. Please log out and log in again.",
                retryable: false,
            }
        }

        // Generic error
        return {
            type: "general",
            message: error.message || "An unexpected error occurred. Please try again.",
            retryable: true,
        }
    }, [])

    // Check for card ID in URL when component mounts or URL changes
    useEffect(() => {
        const cardId = searchParams.get("card")
        if (cardId) {
            setSelectedCardId(cardId)
            setIsSheetOpen(true)
        }
    }, [searchParams])

    // Update URL when sheet is opened/closed
    useEffect(() => {
        if (isSheetOpen && selectedCardId) {
            const url = new URL(window.location.href)
            url.searchParams.set("card", selectedCardId)
            window.history.pushState({}, "", url.toString())
        } else {
            if (searchParams.has("card")) {
                const url = new URL(window.location.href)
                url.searchParams.delete("card")
                window.history.pushState({}, "", url.toString())
            }
        }
    }, [isSheetOpen, selectedCardId, searchParams])

    // Fetch all data in parallel for better performance
    const fetchAllData = useCallback(async () => {
        try {
            setLoading(true)
            setError(null)

            if (!user.recordId) {
                throw new Error("User record ID not found")
            }

            // Add timeout to requests
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error("Request timeout")), 30000)
            })

            // Fetch onboarding details and card images in parallel with timeout
            const [onboardingResponse, cardImagesResponse] = (await Promise.race([
                Promise.all([
                    axiosInstance.get(`b2b/cards/parent/${user.recordId}`),
                    axiosInstance.get<CardImage[]>("/images"),
                ]),
                timeoutPromise,
            ])) as any

            const cardsData = onboardingResponse.data.data

            setCards(cardsData)
            setOnboarding(onboardingResponse.data)
            setCardImages(cardImagesResponse.data)
            setRetryCount(0) // Reset retry count on success

            // Process card images
            if (cardsData.length > 0 && cardImagesResponse.data) {
                processCardImages(
                    cardsData,
                    cardImagesResponse.data,
                    companyData?.parentCompany?._id || user?.company?._id || NEXT_PUBLIC_DEFAULT_COMPANY_ID,
                )
            }
        } catch (error: any) {
            const errorState = handleError(error)
            setError(errorState)
        } finally {
            setLoading(false)
        }
    }, [user.recordId, companyData, handleError])

    // Process card images once we have both cards and images
    const processCardImages = useCallback((cards, images, companyId) => {
        setCardImagesLoading(true)
        try {
            const imageMap: { [key: string]: CardImage } = {}

            cards.forEach((card) => {
                const matchingImages = images.filter((img) => img.product_version.version_code === card.productCode)
                let companyImage = matchingImages.find((img) => img.company._id === companyId)

                if (!companyImage) {
                    companyImage = matchingImages.find((img) => img.company._id === NEXT_PUBLIC_DEFAULT_COMPANY_ID)
                }

                if (companyImage) {
                    imageMap[card.cardKey] = companyImage
                }
            })

            setCurrentCardImages(imageMap)
        } catch (error) {
            console.error("Error processing card images:", error)
        } finally {
            setCardImagesLoading(false)
        }
    }, [])

    useEffect(() => {
        fetchAllData()
    }, [fetchAllData])

    const handleCardClick = useCallback((cardId: string) => {
        setSelectedCardId(cardId)
        setIsSheetOpen(true)
    }, [])

    const handleSheetOpenChange = useCallback((open: boolean) => {
        setIsSheetOpen(open)
        if (!open) {
            const url = new URL(window.location.href)
            url.searchParams.delete("card")
            window.history.pushState({}, "", url.toString())
        }
    }, [])

    const handleRetry = useCallback(() => {
        setRetryCount((prev) => prev + 1)
        fetchAllData()
    }, [fetchAllData])

    // Render error icon based on error type
    const getErrorIcon = (type: ErrorState["type"]) => {
        switch (type) {
            case "network":
                return <WifiOff className="h-12 w-12 text-red-500" />
            case "auth":
                return <Shield className="h-12 w-12 text-orange-500" />
            case "timeout":
                return <Clock className="h-12 w-12 text-yellow-500" />
            case "server":
                return <AlertCircle className="h-12 w-12 text-red-500" />
            case "permission":
                return <Shield className="h-12 w-12 text-red-500" />
            default:
                return <AlertCircle className="h-12 w-12 text-red-500" />
        }
    }

    if (loading) {
        return <LoadingOverlay />
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <Card className="max-w-2xl mx-auto">
                    <CardContent className="pt-6">
                        <div className="text-center space-y-4">
                            {getErrorIcon(error.type)}

                            <div className="space-y-2">
                                <h2 className="text-2xl font-semibold text-gray-900">
                                    {error.type === "network" && "Connection Problem"}
                                    {error.type === "auth" && "Authentication Required"}
                                    {error.type === "timeout" && "Request Timed Out"}
                                    {error.type === "server" && "Server Error"}
                                    {error.type === "permission" && "Access Denied"}
                                    {error.type === "general" && "Something Went Wrong"}
                                </h2>

                                <p className="text-gray-600 max-w-md mx-auto">{error.message}</p>

                                {error.code && <p className="text-sm text-gray-500">Error Code: {error.code}</p>}
                            </div>

                            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                                {error.retryable && (
                                    <Button onClick={handleRetry} className="flex items-center gap-2" disabled={loading}>
                                        <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                                        Try Again
                                        {retryCount > 0 && ` (${retryCount})`}
                                    </Button>
                                )}

                                {error.type === "auth" && (
                                    <Button variant="outline" onClick={() => (window.location.href = "/login")}>
                                        Go to Login
                                    </Button>
                                )}

                                {error.type === "network" && (
                                    <Button variant="outline" onClick={() => window.location.reload()}>
                                        Refresh Page
                                    </Button>
                                )}
                            </div>

                            {error.type === "network" && (
                                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                                    <div className="flex items-center gap-2 text-blue-800 mb-2">
                                        <Wifi className="h-4 w-4" />
                                        <span className="font-medium">Connection Tips:</span>
                                    </div>
                                    <ul className="text-sm text-blue-700 space-y-1 text-left">
                                        <li>• Check your internet connection</li>
                                        <li>• Try switching between WiFi and mobile data</li>
                                        <li>• Disable VPN if you're using one</li>
                                        <li>• Clear your browser cache and cookies</li>
                                    </ul>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold mb-6">Card Management</h1>

            {account && (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
                    <Card>
                        <CardHeader>
                            <CardTitle>Total Cards</CardTitle>
                            <CardDescription>Number of cards associated with your account</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold">{cards?.length || 0}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader>
                            <CardTitle>Active Cards</CardTitle>
                            <CardDescription>Number of currently active cards</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-green-600">{activeCards.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader>
                            <CardTitle>Physical Cards</CardTitle>
                            <CardDescription>
                                Physical cards ({physicalCards.length}/{NEXT_PUBLIC_PHYSICAL_CARD_LIMIT})
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-blue-600">{physicalCards.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader>
                            <CardTitle>Virtual Cards</CardTitle>
                            <CardDescription>
                                Virtual cards ({virtualCards.length}/{NEXT_PUBLIC_VIRTUAL_CARD_LIMIT})
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-purple-600">{virtualCards.length}</div>
                        </CardContent>
                    </Card>
                </div>
            )}

            <Tabs defaultValue="all" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="all">All Cards ({cards?.length || 0})</TabsTrigger>
                    <TabsTrigger value="active">Active ({activeCards.length})</TabsTrigger>
                    <TabsTrigger value="inactive">Inactive ({inactiveCards.length})</TabsTrigger>
                    <TabsTrigger value="physical">Physical ({physicalCards.length})</TabsTrigger>
                    <TabsTrigger value="virtual">Virtual ({virtualCards.length})</TabsTrigger>
                </TabsList>

                <TabsContent value="all">
                    <CardList
                        cards={cards || []}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="All Cards"
                        description="All cards associated with your account"
                        exportData={cardsData}
                    />
                </TabsContent>

                <TabsContent value="active">
                    <CardList
                        cards={activeCards}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="Active Cards"
                        description="Currently active cards"
                        exportData={cardsData.filter((card) => card.status.toUpperCase() === "ACTIVE")}
                    />
                </TabsContent>

                <TabsContent value="inactive">
                    <CardList
                        cards={inactiveCards}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="Inactive Cards"
                        description="Cards that are not currently active"
                        exportData={cardsData.filter((card) => card.status.toUpperCase() !== "ACTIVE")}
                    />
                </TabsContent>

                <TabsContent value="physical">
                    <CardList
                        cards={physicalCards}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="Physical Cards"
                        description={`Physical cards (${physicalCards.length}/${NEXT_PUBLIC_PHYSICAL_CARD_LIMIT} limit)`}
                        exportData={cardsData.filter((card) => card.type === "Physical")}
                    />
                </TabsContent>

                <TabsContent value="virtual">
                    <CardList
                        cards={virtualCards}
                        onCardClick={handleCardClick}
                        currentCardImages={currentCardImages}
                        cardImagesLoading={cardImagesLoading}
                        title="Virtual Cards"
                        description={`Virtual cards (${virtualCards.length}/${NEXT_PUBLIC_VIRTUAL_CARD_LIMIT} limit)`}
                        exportData={cardsData.filter((card) => card.type === "Virtual")}
                    />
                </TabsContent>

                {(onboarding || cards?.length > 0) && (
                    <CardDetailsSheet
                        companyId={companyData?.parentCompany?._id || user?.company?._id || NEXT_PUBLIC_DEFAULT_COMPANY_ID}
                        isOpen={isSheetOpen}
                        onOpenChange={handleSheetOpenChange}
                        cardId={selectedCardId || ""}
                    />
                )}
            </Tabs>
        </div>
    )
}

interface CardListProps {
    cards: any[]
    onCardClick: (cardId: string) => void
    currentCardImages: { [key: string]: CardImage }
    cardImagesLoading: boolean
    title: string
    description: string
    exportData: any[]
}

// Utility function
function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    })
}

function getStatusColor(status: string) {
    switch (status.toUpperCase()) {
        case "ACTIVE":
            return "bg-green-500"
        case "INACTIVE":
            return "bg-yellow-500"
        case "BLOCKED":
            return "bg-red-500"
        case "ORDERED":
            return "bg-blue-500"
        default:
            return "bg-gray-500"
    }
}

function CardList({
                      cards,
                      onCardClick,
                      currentCardImages,
                      cardImagesLoading,
                      title,
                      description,
                      exportData,
                  }: CardListProps) {
    // Render card image with proper loading and fallback
    const renderCardImage = (cardKey: string) => {
        if (cardImagesLoading) {
            return <Skeleton className="h-[25px] w-[40px] rounded" />
        }

        const cardImage = currentCardImages[cardKey]
        const imageSrc = cardImage ? `${asset}/${cardImage.front_side}` : "/cards/pp-fd.svg"

        return (
            <Image
                src={imageSrc || "/placeholder.svg"}
                alt="Credit card"
                width={40}
                height={25}
                className="object-cover"
                priority={true}
                unoptimized
                onError={(e) => {
                    // Fallback to default image on error
                    e.currentTarget.src = "/cards/pp-fd.svg"
                }}
            />
        )
    }

    if (!cards || cards.length === 0) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>{title}</CardTitle>
                    <CardDescription>{description}</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="bg-muted/20 p-8 rounded-lg text-center">
                        <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h4 className="text-lg font-medium mb-2">No Cards Found</h4>
                        <p className="text-muted-foreground max-w-md mx-auto">
                            {title === "All Cards"
                                ? "You don't have any cards yet. Contact your administrator to request a card."
                                : `No cards found in this category.`}
                        </p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>
                    {title} ({cards.length})
                </CardTitle>
                <CardDescription>{description}</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="mb-4">
                    <DataExporter
                        data={exportData}
                        filename={`${title.toLowerCase().replace(/\s+/g, "_")}_cards`}
                        title={`${title} Report`}
                    />
                </div>
                <div className="border rounded-lg">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Card</TableHead>
                                <TableHead>Name</TableHead>
                                <TableHead>Number</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Type</TableHead>
                                <TableHead>Expiry</TableHead>
                                <TableHead>Created</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {cards.map((card, index) => (
                                <TableRow
                                    onClick={() => onCardClick(card.cardKey)}
                                    key={index}
                                    className="cursor-pointer hover:bg-muted/50"
                                >
                                    <TableCell>{renderCardImage(card.cardKey)}</TableCell>
                                    <TableCell className="font-medium">{card.nickName ? card.nickName : card.embossName1}</TableCell>
                                    <TableCell className="font-mono">•••• {card.cardMask.slice(-4)}</TableCell>
                                    <TableCell>
                                        <Badge className={getStatusColor(card.status)}>{card.status}</Badge>
                                    </TableCell>
                                    <TableCell>
                                        <Badge variant="outline">
                                            {card.productDesc && card.productDesc.includes("PHY") ? "Physical" : "Virtual"}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>{card.expDate}</TableCell>
                                    <TableCell>{formatDate(card.createdAt)}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
    )
}
