//@ts-nocheck
"use client"

import { useEffect, useState } from "react"
import type React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import axios from "@/utils/axiosInstance"
import { ArrowUpDown, TrashIcon } from "lucide-react"
import { formatDate } from "@/utils/helpers"
import { Tabs } from "flowbite-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAppSelector } from "@/store/hooks"
import { ActionsButtonGroup } from "@/components/ActionButtons"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { Alert } from "@/components/alert"
import DataExporter from "@/components/DataExporter"

interface BinType {
    _id: number
    type: string
    cardScheme: string
    binVariant: string
    binCategory: string
    programmeType: string
    reason: string
    created_at: string
    created_by: string
}

interface Role {
    permissions: string[]
}

interface Message {
    type: "success" | "error"
    content: string
}

export default function BinType() {
    const [formData, setFormData] = useState({
        cardScheme: "",
        binVariant: "",
        binCategory: "",
        programmeType: "",
        type: "",
        created_by: localStorage.getItem("user"),
    })

    const [cardSchemes, setCardSchemes] = useState<any[]>([])
    const [savedbinTypes, setSavedbinTypes] = useState<BinType[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<{ key: keyof BinType; direction: "asc" | "desc" } | null>(null)
    const [error, setError] = useState<string>("")
    const [selectedSchemeName, setSelectedSchemeName] = useState<string>("")
    const [deleteId, setDeleteId] = useState<number | null>(null)
    const [countries, setCountries] = useState([])
    const [isLoading, setIsLoading] = useState(false)
    const [alert, setAlert] = useState<{ message: string; type: "success" | "error" } | null>(null)
    const [isOverlayVisible, setIsOverlayVisible] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [savedBinCategories, setSavedBinCategories] = useState([])
    const [savedBinVariants, setSavedBinCVariants] = useState([])
    const [savedProgrammeTypes, setSavedProgrammeTypes] = useState([])
    const [validationError, setValidationError] = useState<string | null>(null)
    const [sheetOpen, setSheetOpen] = useState(false)

    // Combine all permissions
    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
    }, [])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions)) // Remove duplicates
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        // If roles array is empty, allow all permissions
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "BIN Type_Request")
    const userHasDeletePermission = hasPermission(roles, "BIN Type_Delete")
    const userHasApprovePermission = hasPermission(roles, "BIN Type_Approve")
    const userHasViewPermission = hasPermission(roles, "BIN Type_View")

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        // Check if the type already exists with the same currency
        const existingType = savedbinTypes.find(
            (type) => type.type.toLowerCase() === formData.type.toLowerCase() && type.currency._id === formData.currency,
        )

        if (existingType) {
            setValidationError("This type already exists with the selected currency.")
            return
        }

        // Check if the type exists with a different currency
        const typeExists = savedbinTypes.some(
            (type) => type.type.toLowerCase() === formData.type.toLowerCase() && type.currency._id !== formData.currency,
        )

        if (!typeExists) {
            // If the type doesn't exist at all, we need to check if it's unique
            const isUnique = !savedbinTypes.some((type) => type.type.toLowerCase() === formData.type.toLowerCase())
            if (!isUnique) {
                setValidationError("This type already exists. It must be unique or used with a different currency.")
                return
            }
        }

        setValidationError(null)
        await confirmSubmit(e)
    }

    useEffect(() => {
        fetchbinTypes()
    }, [])

    const fetchbinTypes = async () => {
        setIsLoading(true)
        try {
            const [schemes, types, currencies, categories, variants, pts] = await Promise.all([
                axios.get("cardScheme/schemes"),
                axios.get("/bin-types"),
                axios.get("product-currencies"),
                axios.get("/bin-category"),
                axios.get("/bin-variant"),
                axios.get("/programme-type"),
            ])

            setCardSchemes(schemes.data.data)
            setSavedbinTypes(types.data.data)
            setCountries(currencies.data)
            setSavedBinCategories(categories.data.data)
            setSavedBinCVariants(variants.data.data)
            setSavedProgrammeTypes(pts.data)
        } catch (error) {
            console.error("Error fetching BIN Types", error)
            setAlert({ message: "Error fetching data. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement> | string, fieldName?: string) => {
        let name, value
        if (typeof e === "string") {
            name = fieldName
            value = e
        } else {
            name = e.target.name
            value = e.target.value
        }

        // Generate type based on selections
        const generateType = (updatedFormData = formData) => {
            const scheme = cardSchemes.find((scheme) => scheme._id === updatedFormData.cardScheme)
            const programmeType = savedProgrammeTypes.find((pt) => pt._id === updatedFormData.programmeType)
            const variant = savedBinVariants.find((v) => v._id === updatedFormData.binVariant)
            const category = savedBinCategories.find((c) => c._id === updatedFormData.binCategory)

            let typeString = ""
            if (scheme) {
                typeString += scheme.scheme_name.toUpperCase()
            }

            if (programmeType) {
                typeString += ` ${programmeType.type.toUpperCase()}`
            }

            if (variant) {
                typeString += ` ${variant.variant.toUpperCase()}`
            }

            if (category) {
                typeString += ` ${category.category.toUpperCase()}`
            }

            return typeString.trim()
        }

        if (name === "binCategory") {
            const updatedFormData = { ...formData, binCategory: value }
            setFormData((prev) => ({
                ...prev,
                type: generateType(updatedFormData),
                binCategory: value,
            }))
        } else if (name === "programmeType") {
            const updatedFormData = { ...formData, programmeType: value }
            setFormData((prev) => ({
                ...prev,
                type: generateType(updatedFormData),
                programmeType: value,
            }))
        } else {
            if (name === "type") {
                const isDuplicate = savedbinTypes.some((scheme) => scheme.type.toLowerCase() === value.toLowerCase())
                setError(isDuplicate ? "This BIN Type already exists. Please enter a unique name." : "")
            }

            if (name === "binCodeSuffix") {
                const binCode = `${formData.binCodePrefix}${value}`
                setFormData((prev) => ({ ...prev, binCode }))
            }

            setFormData((prev) => {
                const updatedFormData = { ...prev, [name]: value }
                return {
                    ...updatedFormData,
                    type: generateType(updatedFormData),
                }
            })
        }
    }

    const handleCardSchemeChange = (schemeId: string) => {
        const selectedScheme = cardSchemes.find((scheme) => scheme._id === schemeId)
        const selectedProgrammeType = savedProgrammeTypes.find((scheme) => scheme._id === formData.programmeType)

        let typeString = selectedScheme.scheme_name.toUpperCase()

        if (selectedProgrammeType) {
            typeString += ` ${selectedProgrammeType.type.toUpperCase()}`
        }

        if (formData.binVariant) {
            const variant = savedBinVariants.find((v) => v._id === formData.binVariant)
            if (variant) typeString += ` ${variant.variant.toUpperCase()}`
        }

        if (formData.binCategory) {
            const category = savedBinCategories.find((c) => c._id === formData.binCategory)
            if (category) typeString += ` ${category.category.toUpperCase()}`
        }

        setSelectedSchemeName(selectedScheme.scheme_name.toUpperCase())
        setFormData((prev) => ({
            ...prev,
            cardScheme: schemeId,
            type: typeString,
        }))
    }

    const handleCurrencyChange = (currency: any) => {
        setFormData((prev) => ({
            ...prev,
            currency: currency,
        }))
    }

    const confirmSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)
        try {
            await axios.post("/bin-types", formData)
            await fetchbinTypes()
            setFormData({
                cardScheme: "",
                type: "",
                programmeType: "",
                binVariant: "",
                binCategory: "",
                created_by: localStorage.getItem("user"),
            })
            setAlert({ message: "BIN Type created successfully!", type: "success" })
            setSheetOpen(false) // Close the sheet after successful submission
        } catch (error) {
            console.error("Error creating BIN Type", error)
            setAlert({ message: "Error creating BIN Type. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const handleDelete = async (deleteId) => {
        if (deleteId === null) return
        setIsLoading(true)
        setIsOverlayVisible(true)
        try {
            await axios.delete(`/bin-types/${deleteId}`)
            setSavedbinTypes((prev) => prev.filter((type) => type._id !== deleteId))
            setDeleteId(null)
            await fetchbinTypes()
            setAlert({ message: "BIN Type deleted successfully!", type: "success" })
        } catch (error) {
            console.error("Error deleting BIN Type", error)
            setAlert({ message: "Error deleting BIN Type. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
            setIsOverlayVisible(false)
            setDeleteModalOpen(false)
        }
    }

    const handleSort = (key: keyof BinType) => {
        let direction: "asc" | "desc" = "asc"
        if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc"
        }
        setSortConfig({ key, direction })
    }

    const filteredAndSortedTypes = savedbinTypes
        .filter(
            (type) =>
                type.type.toLowerCase().includes(searchTerm.toLowerCase()) ,
        )
        .sort((a, b) => {
            if (sortConfig === null) {
                return 0
            }
            const { key, direction } = sortConfig
            if (a[key] < b[key]) {
                return direction === "asc" ? -1 : 1
            }
            if (a[key] > b[key]) {
                return direction === "asc" ? 1 : -1
            }
            return 0
        })

    const handleDeleteClick = (id: string) => {
        setDeleteId(Number.parseInt(id))
        setDeleteModalOpen(true)
    }

    const renderTable = (status: string) => {
        const data = savedbinTypes
            .filter((row) => row.status.toLowerCase() === status.toLowerCase()) // Filtering by status
            .map((row) => ({
                id: row._id,
                bin_type: row.type,
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase(),
            }))

        return (
            <>
                <DataExporter data={data} filename="bin_types" title="BIN Type Report" />
                <Table className="overflow-x-auto">
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[100px]">ID</TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort("type")}>
                                Type <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                            </TableHead>

                            <TableHead   >
                                BIN Code
                            </TableHead>

                            <TableHead    >
                                Currency
                            </TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort("created_by")}>
                                Created By <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                            </TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort("created_at")}>
                                Created At <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                            </TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredAndSortedTypes
                            .filter((type) => type.status.toLowerCase() === status)
                            .map((type, index) => (
                                <TableRow key={type._id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{type.type}</TableCell>
                                    <TableCell>{type.binCategory?.bin_prefix}{type.binVariant?.bin_suffix}</TableCell>
                                    <TableCell>{type.binCategory?.currency?.currency_code}</TableCell>
                                    <TableCell>{type.created_by?.name || "N/A"}</TableCell>
                                    <TableCell>{formatDate(type.created_at)}</TableCell>
                                    <TableCell>{type.status.toUpperCase()}</TableCell>
                                    <TableCell>
                                        {userHasDeletePermission && status === "active" && (
                                            <AlertDialog>
                                                <AlertDialogTrigger asChild>
                                                    <Button variant="destructive" size="sm" onClick={() => setDeleteId(type._id)}>
                                                        <TrashIcon className="h-4 w-4" />
                                                    </Button>
                                                </AlertDialogTrigger>
                                                <AlertDialogContent>
                                                    <AlertDialogHeader>
                                                        <AlertDialogTitle>Action: Delete</AlertDialogTitle>
                                                        <AlertDialogDescription>
                                                            Deletion of this record will be sent to administrator for approval. Are you sure to
                                                            proceed?
                                                        </AlertDialogDescription>
                                                    </AlertDialogHeader>
                                                    <AlertDialogFooter>
                                                        <AlertDialogCancel onClick={() => setDeleteId(null)}>Cancel</AlertDialogCancel>
                                                        <AlertDialogAction onClick={() => handleDelete(type._id)}>Delete</AlertDialogAction>
                                                    </AlertDialogFooter>
                                                </AlertDialogContent>
                                            </AlertDialog>
                                        )}
                                        {status !== "active" && (
                                            <ActionsButtonGroup
                                                entity={type}
                                                entityType="bin-types" // For BIN Type
                                                entityName={type.type}
                                                userHasApprovePermission={userHasApprovePermission}
                                                userHasDeletePermission={userHasViewPermission}
                                                handleDeleteClick={handleDelete}
                                                fetchEntities={fetchbinTypes}
                                            />
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
            </>
        )
    }

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            {isLoading && <LoadingOverlay />}
            {isOverlayVisible && <div className="fixed inset-0 bg-black bg-opacity-50 z-50" />}
            <div className="flex flex-col gap-4 md:flex-row md:gap-6">
                <div className="w-full">
                    <Card>
                        <CardHeader className="flex-row items-center justify-between">
                            <div>
                                <CardTitle>BIN Type</CardTitle>
                                <CardDescription>View and Request BIN Types</CardDescription>
                            </div>
                            {userHasRequestPermission && (
                                <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
                                    <SheetTrigger asChild>
                                        <Button onClick={() => setSheetOpen(true)}>Request New BIN</Button>
                                    </SheetTrigger>
                                    <SheetContent className="max-h-screen overflow-y-auto bg-white">
                                        <SheetHeader>
                                            <SheetTitle>BIN Type Request</SheetTitle>
                                        </SheetHeader>
                                        <form onSubmit={handleSubmit} className="space-y-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="cardScheme">Card Scheme</Label>
                                                <Select disabled={false} onValueChange={handleCardSchemeChange}>
                                                    <SelectTrigger id="cardScheme">
                                                        <SelectValue placeholder="Select Card Scheme" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {cardSchemes
                                                            .filter((s) => s.status === "active")
                                                            .map((scheme) => (
                                                                <SelectItem key={scheme._id} value={scheme._id}>
                                                                    {scheme.scheme_name}
                                                                </SelectItem>
                                                            ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="programmeType">Programme Type</Label>
                                                <Select
                                                    disabled={!formData.cardScheme}
                                                    onValueChange={(value) => handleInputChange(value, "programmeType")}
                                                >
                                                    <SelectTrigger id="programmeType">
                                                        <SelectValue placeholder="Select Programme Type" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {savedProgrammeTypes.map((s) => (
                                                            <SelectItem key={s._id} value={s._id}>
                                                                {s.type.toUpperCase()}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="binVariant">BIN Variant</Label>
                                                <Select
                                                    disabled={!formData.programmeType}
                                                    onValueChange={(value) => handleInputChange(value, "binVariant")}
                                                >
                                                    <SelectTrigger id="binVariant">
                                                        <SelectValue placeholder="Select BIN Variant" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {savedBinVariants
                                                            .filter((s) => s.status === "active")
                                                            .map((scheme) => (
                                                                <SelectItem key={scheme._id} value={scheme._id}>
                                                                    {scheme.variant.toUpperCase()}
                                                                </SelectItem>
                                                            ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="binCategory">BIN Category</Label>
                                                <Select
                                                    disabled={!formData.binVariant}
                                                    onValueChange={(value) => handleInputChange(value, "binCategory")}
                                                >
                                                    <SelectTrigger id="binCategory">
                                                        <SelectValue placeholder="Select BIN Category" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {savedBinCategories
                                                            .filter((s) => s.status === "active")
                                                            .map((scheme) => (
                                                                <SelectItem key={scheme._id} value={scheme._id}>
                                                                    {scheme.category.toUpperCase()}
                                                                </SelectItem>
                                                            ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="type">Type</Label>
                                                <Input
                                                    id="type"
                                                    name="type"
                                                    value={formData.type.toUpperCase()}
                                                    readOnly
                                                    placeholder="Type will be auto-generated"
                                                />
                                            </div>

                                            {error && <p className="text-red-600">{error}</p>}
                                            {validationError && <div className="text-red-500 text-sm mt-2">{validationError}</div>}

                                            <div className="flex justify-between">
                                                <SheetClose asChild>
                                                    <Button type="button" variant="outline">
                                                        Cancel
                                                    </Button>
                                                </SheetClose>
                                                <Button disabled={!!error || !!validationError} type="submit">
                                                    Save
                                                </Button>
                                            </div>
                                        </form>
                                    </SheetContent>
                                </Sheet>
                            )}
                        </CardHeader>
                        {userHasViewPermission && (
                            <CardContent>
                                {alert && <Alert message={alert.message} type={alert.type} onClose={() => setAlert(null)} />}
                                <div className="mb-4">
                                    <Input
                                        placeholder="Search BIN Types..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                                <Tabs aria-label="Tabs with underline" variant="underline">
                                    <Tabs.Item active title="Approved">
                                        {renderTable("active")}
                                    </Tabs.Item>
                                    <Tabs.Item title="Pending">{renderTable("pending")}</Tabs.Item>
                                    <Tabs.Item title="Modify">{renderTable("modify")}</Tabs.Item>
                                    <Tabs.Item title="Declined">{renderTable("decline")}</Tabs.Item>
                                </Tabs>
                            </CardContent>
                        )}
                    </Card>
                </div>
            </div>
            <Dialog
                open={deleteModalOpen}
                onOpenChange={(open) => {
                    setDeleteModalOpen(open)
                    setIsOverlayVisible(open)
                }}
            >
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Action: Delete</DialogTitle>
                        <DialogDescription>
                            Deletion of this record will be sent to administrator for approval. Are you sure to proceed?
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDelete}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
