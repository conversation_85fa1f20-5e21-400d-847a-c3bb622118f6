//@ts-nocheck
"use client"

import {useEffect, use<PERSON>em<PERSON>, useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import {ArrowUpDown, TrashIcon} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import {formatDate} from "@/utils/helpers"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    <PERSON><PERSON><PERSON><PERSON>ogHeader,
    <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog"
import {Tabs} from "flowbite-react"
import {useAppSelector} from "@/store/hooks"
import {SchemeActionsButtonGroup} from "@/app/lite/admin/configurations/card-scheme/SchemeActionsDropdown"
import {LoadingOverlay} from "@/components/LoadingOverlay"
import DataExporter from "@/components/DataExporter";


interface CardScheme {
    _id: string
    scheme_name: string
    status: string
    version: number
    reason: string
    created_at: string
    created_by: string
}

interface Role {
    _id: string // Unique identifier for the role
    name: string // Name of the role (e.g., Admin)
    description: string // Description of the role
    permissions: string[] // Array of permissions assigned to the role
    createdAt: string // ISO string representation of the creation timestamp
    updatedAt: string // ISO string representation of the last update timestamp
}

export default function CardScheme() {
    const [formData, setFormData] = useState({
        scheme_name: "",
        scheme_code: "",
    })
    const [savedSchemes, setSavedSchemes] = useState<CardScheme[]>([])
    const [roles, setRoles] = useState<Role[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<{ key: keyof CardScheme; direction: "asc" | "desc" } | null>(null)
    const [error, setError] = useState("")
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [schemeToDelete, setSchemeToDelete] = useState<string | null>(null)
    const user = useAppSelector((state) => state.user.user)
    const [selectedScheme, setSelectedScheme] = useState(null)
    const [dialogAction, setDialogAction] = useState("")
    const [isLoading, setIsLoading] = useState(false) // Added loading state

    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
    const [activeTab, setActiveTab] = useState("Approved")

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsConfirmDialogOpen(true)
    }
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({ ...prev, [name]: value }))

        if (name === "scheme_name") {
            const isDuplicate = savedSchemes.some((scheme) => scheme.scheme_name.toLowerCase() === value.toLowerCase())
            setError(isDuplicate ? "This Scheme already exists. Please enter a unique name." : "")
        }
    }

    const confirmSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsConfirmDialogOpen(false)
        if (error) {
            console.error("Form submission error:", error)
            return
        }

        const formDataToSend = new FormData()
        formDataToSend.append("scheme_name", formData.scheme_name)
        formDataToSend.append("scheme_code", formData.scheme_code)
        formDataToSend.append("created_by", localStorage.getItem("user"))

        setIsLoading(true)
        try {
            await axiosInstance.post("cardScheme/schemes", formDataToSend)
            await fetchSchemes()
            setActiveTab("Pending")
            setFormData({ scheme_name: "", scheme_code: "" })
        } catch (error) {
            console.error("Error saving scheme:", error)
        } finally {
            setIsLoading(false)
        }
    }

    useEffect(() => {
        setRoles(user.roles)
        if (savedSchemes.length <= 0) {
            fetchSchemes()
        }
    }, [])
    const fetchSchemes = async () => {
        setIsLoading(true)
        try {
            const response = await axiosInstance.get("cardScheme/schemes")
            setSavedSchemes(response.data.data)
        } catch (error) {
            console.error("Error fetching schemes:", error)
        } finally {
            setIsLoading(false)
        }
    }
    const handleDeleteClick = (id: string) => {
        setSchemeToDelete(id)
        setDeleteModalOpen(true)
    }

    const handleDeleteConfirm = async () => {
        if (schemeToDelete) {
            setIsLoading(true)
            try {
                await axiosInstance.delete(`cardScheme/schemes/${schemeToDelete}`)
                setSavedSchemes((prev) => prev.filter((scheme) => scheme._id !== schemeToDelete))
                setDeleteModalOpen(false)
                setSchemeToDelete(null)
            } catch (error) {
                console.error("Error deleting scheme:", error)
            } finally {
                setIsLoading(false)
            }
        }
    }

    const sortData = (key: keyof CardScheme) => {
        let direction: "asc" | "desc" = "asc"
        if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc"
        }
        setSortConfig({ key, direction })
    }

    const filteredAndSortedSchemes = useMemo(() => {
        console.log(savedSchemes)
        const filteredSchemes = savedSchemes.filter(
            (scheme) =>
                scheme.scheme_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                scheme.scheme_code.toLowerCase().includes(searchTerm.toLowerCase()),
        )
        return filteredSchemes.sort((a, b) => {
            if (sortConfig === null) {
                return 0
            }
            const { key, direction } = sortConfig
            if (a[key] < b[key]) {
                return direction === "asc" ? -1 : 1
            }
            if (a[key] > b[key]) {
                return direction === "asc" ? 1 : -1
            }
            return 0
        })
    }, [savedSchemes, searchTerm, sortConfig])

    // Combine all permissions
    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions)) // Remove duplicates
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        // If roles array is empty, allow all permissions
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Card Scheme_Request")
    const userHasDeletePermission = hasPermission(roles, "Card Scheme_Delete")
    const userHasApprovePermission = hasPermission(roles, "Card Scheme_Approve")
    const userHasViewPermission = hasPermission(roles, "Card Scheme_View")

    const renderTable = (status: string) => {
        const data = savedSchemes
            .filter(row => row.status.toLowerCase() === status.toLowerCase()) // Filtering by status
            .map(row => ({
                id: row._id,
                scheme_name: row.scheme_name,
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase()
            }));

        return (
            <>
                <DataExporter
                    data={data}
                    filename="card_scheme"
                    title="Card Scheme Report"
                 />
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead onClick={() => sortData("_id")} className="cursor-pointer">
                                ID <ArrowUpDown className="ml-2 h-4 w-4" />
                            </TableHead>
                            <TableHead onClick={() => sortData("scheme_name")} className="cursor-pointer">
                                Scheme Name <ArrowUpDown className="ml-2 h-4 w-4" />
                            </TableHead>
                            <TableHead onClick={() => sortData("created_by")} className="cursor-pointer">
                                Created By <ArrowUpDown className="ml-2 h-4 w-4" />
                            </TableHead>
                            <TableHead onClick={() => sortData("created_at")} className="cursor-pointer">
                                Created At <ArrowUpDown className="ml-2 h-4 w-4" />
                            </TableHead>
                            <TableHead onClick={() => sortData("status")} className="cursor-pointer">
                                Status <ArrowUpDown className="ml-2 h-4 w-4" />
                            </TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredAndSortedSchemes
                            .filter(type => type.status.toLowerCase() === status.toLowerCase())
                            .map((scheme, index) => (
                                <TableRow key={scheme._id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{scheme.scheme_name}</TableCell>
                                    <TableCell>{scheme.created_by?.name || "N/A"}</TableCell>
                                    <TableCell>{formatDate(scheme.created_at)}</TableCell>
                                    <TableCell>{scheme.status.toUpperCase()}</TableCell>
                                    <TableCell>
                                        {userHasDeletePermission && status === "active" && (
                                            <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(scheme._id)}>
                                                <TrashIcon className="h-4 w-4" />
                                            </Button>
                                        )}
                                        {status !== "active" && (
                                            <SchemeActionsButtonGroup
                                                scheme={scheme}
                                                userHasEditPermission={true}
                                                userHasApprovePermission={userHasApprovePermission}
                                                userHasDeletePermission={userHasDeletePermission}
                                                handleDeleteClick={handleDeleteClick}
                                                fetchCardScheme={fetchSchemes}
                                            />
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
            </>
        );
    };

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            {isLoading && <LoadingOverlay/>} {/* Added LoadingOverlay */}
            <div className="flex flex-col gap-4 md:flex-row md:gap-6">
                {userHasRequestPermission && (
                    <div className="w-full md:w-1/3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Card Scheme Request</CardTitle>
                                <CardDescription>Add Card Scheme</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="scheme_name">Card Scheme</Label>
                                        <Input
                                            id="scheme_name"
                                            name="scheme_name"
                                            placeholder="Scheme Name e.g Visa, UPI"
                                            value={formData.scheme_name}
                                            onChange={handleInputChange}
                                            required
                                        />
                                        {error && <p className="text-red-600">{error}</p>}
                                    </div>

                                    <div className="flex justify-between">
                                        <Button type="button" variant="outline">
                                            Cancel
                                        </Button>
                                        <Button disabled={!!error} type="submit">
                                            Request
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                )}
                {userHasViewPermission && (
                    <div className="w-full md:w-2/3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Saved Card Schemes</CardTitle>
                                <CardDescription>View and manage your saved card schemes</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="mb-4">
                                    <Input
                                        placeholder="Search schemes..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                                <Tabs
                                    aria-label="Tabs with underline"
                                    variant="underline"
                                    value={activeTab}
                                    onValueChange={setActiveTab}
                                >
                                    <Tabs.Item  active title="Approved" value="Approved">
                                        {renderTable("active")}
                                    </Tabs.Item>

                                    <Tabs.Item title="Pending"  value="Pending">
                                        {renderTable("pending")}
                                    </Tabs.Item>

                                    <Tabs.Item title="Modification" value="Modification">
                                        {renderTable("modify")}
                                    </Tabs.Item>
                                    <Tabs.Item active={activeTab === "Declined"} title="Declined" value="Declined">
                                        {renderTable("decline")}
                                    </Tabs.Item>
                                </Tabs>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
            <Dialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Action: Delete</DialogTitle>
                        <DialogDescription>
                            Deletion of this record will be sent to administrator for approval. Are you sure to proceed?
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDeleteConfirm}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Submission</AlertDialogTitle>
                        <AlertDialogDescription>
                            Do you want to submit this record? This record will be pending for approval!
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmSubmit}>Submit</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}

