//@ts-nocheck
"use client"

import React, {useEffect, useState} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
import {Input} from "@/components/ui/input"
import {Switch} from "@/components/ui/switch"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger
} from "@/components/ui/alert-dialog"
import {Alert, AlertDescription, AlertTitle} from "@/components/ui/alert"
import axios from "@/utils/axiosInstance"
import {AlertTriangle, ArrowUpDown, CheckCircle2, Loader2, <PERSON>rashIcon} from "lucide-react"
import {formatDate} from "@/utils/helpers"
import {countries, country_currency} from "@/utils/data"
import {CountrySelect} from "@/app/lite/admin/configurations/country/combobox"
import {Tabs} from "flowbite-react";
import {useAppSelector} from "@/store/hooks";
import {ActionsButtonGroup} from "@/components/ActionButtons";
import DataExporter from "@/components/DataExporter";

interface Country {
    _id: number
    country_name: string
    country_code: string
    currency_code: string
    created_at: string
    created_by: string
    is_active: boolean
}

export default function CountryManager() {
    const [formData, setFormData] = useState({
        country_name: "",
        country_code: "",
        currency_code: "",
        created_by: localStorage.getItem("user")
    })
    const [savedCountries, setSavedCountries] = useState<Country[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [tableSortConfig, setTableSortConfig] = useState<{
        key: keyof Country;
        direction: 'asc' | 'desc'
    } | null>(null)
    const [error, setError] = useState<string>("")
    const [countryToDelete, setCountryToDelete] = useState<Country | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [isFetching, setIsFetching] = useState(false)
    const [showSuccessAlert, setShowSuccessAlert] = useState(false)
    const [showErrorAlert, setShowErrorAlert] = useState(false)
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
// Combine all permissions
    const user = useAppSelector((state) => state.user.user);
    const [roles, setRoles] = useState<Role[]>([])
    useEffect(() => {
        setRoles(user.roles)
    }, [])
    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions);
        return Array.from(new Set(allPermissions)); // Remove duplicates
    };

    const hasPermission = (roles: Role[], permission: string): boolean => {
        // If roles array is empty, allow all permissions
        if (!roles || roles.length === 0) {
            return true;
        }
        const allPermissions = getAllPermissions(roles);
        return allPermissions.includes(permission);
    };

    const userHasRequestPermission = hasPermission(roles, "Product Currency_Request"); // true
    const userHasDeletePermission = hasPermission(roles, "Product Currency_Delete"); // true
    const userHasApprovePermission = hasPermission(roles, "Product Currency_Approve"); // true
    const userHasViewPermission = hasPermission(roles, "Product Currency_View"); // false

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsConfirmDialogOpen(true)
    }
    useEffect(() => {
        fetchCountries()
    }, [])

    const fetchCountries = async () => {
        setIsFetching(true)
        try {
            const {data} = await axios.get("/countries")
            setSavedCountries(data.data)
        } catch (error) {
            console.error("Error fetching countries", error)
            setError("Failed to fetch countries. Please try again.")
            setShowErrorAlert(true)
        } finally {
            setIsFetching(false)
        }
    }

    const handleCountryChange = (countryName: string) => {
        const selectedCountry = countries.find((country) => country.name === countryName)
        if (selectedCountry) {
            setFormData({
                country_name: selectedCountry.name,
                country_code: selectedCountry.code,
                currency_code: "NA",
                  created_by: localStorage.getItem("user")
            })
            setError("")
            const isDuplicate = savedCountries.some(
                (scheme) => scheme.status === "active" && scheme.country_name.toLowerCase() === selectedCountry!.name.toLowerCase()
            )
            setError(isDuplicate ? "This Country already exists. Please try again." : "")
        }


    }

    const confirmSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsConfirmDialogOpen(false)
        setIsLoading(true)
        try {
            await axios.post("/countries", formData)
            setFormData({country_name: "", country_code: "", currency_code: "", created_by: "admin"})
            await fetchCountries()
            setShowSuccessAlert(true)
            setTimeout(() => setShowSuccessAlert(false), 5000) // Hide alert after 5 seconds
        } catch (error) {
            console.error("Error adding country", error)
            setError("Failed to add country. Please try again.")
            setShowErrorAlert(true)
            setTimeout(() => setShowErrorAlert(false), 5000) // Hide error alert after 5 seconds
        } finally {
            setIsLoading(false)
        }
    }

    const handleDelete = async (country) => {

        setIsLoading(true)
        try {
            await axios.delete(`/countries/${country}`)
            setSavedCountries((prev) => prev.filter((c) => c._id !== country._id))
            setCountryToDelete(null)
            setShowSuccessAlert(true)
            setTimeout(() => setShowSuccessAlert(false), 5000) // Hide alert after 5 seconds
        } catch (error) {
            console.error("Error deleting country", error)
            setError("Failed to delete country. Please try again.")
            setShowErrorAlert(true)
            setTimeout(() => setShowErrorAlert(false), 5000) // Hide error alert after 5 seconds
        } finally {
            setIsLoading(false)
        }
    }

    const handleToggleStatus = async (id: number, currentStatus: boolean) => {
        setIsLoading(true)
        try {
            await axios.patch(`/countries/${id}/status`, {is_active: !currentStatus})
            setSavedCountries((prev) =>
                prev.map((country) =>
                    country._id === id ? {...country, is_active: !currentStatus} : country
                )
            )
            setShowSuccessAlert(true)
            setTimeout(() => setShowSuccessAlert(false), 5000) // Hide alert after 5 seconds
        } catch (error) {
            console.error("Error updating country status", error)
            setError("Failed to update country status. Please try again.")
            setShowErrorAlert(true)
            setTimeout(() => setShowErrorAlert(false), 5000) // Hide error alert after 5 seconds
        } finally {
            setIsLoading(false)
        }
    }

    const handleSort = (key: keyof Country) => {
        let direction: 'asc' | 'desc' = 'asc'
        if (tableSortConfig && tableSortConfig.key === key && tableSortConfig.direction === 'asc') {
            direction = 'desc'
        }
        setTableSortConfig({key, direction})
    }

    const sortedCountries = [...savedCountries].sort((a, b) => {
        if (tableSortConfig === null) {
            return 0
        }
        const {key, direction} = tableSortConfig
        if (a[key] < b[key]) {
            return direction === 'asc' ? -1 : 1
        }
        if (a[key] > b[key]) {
            return direction === 'asc' ? 1 : -1
        }
        return 0
    })

    const filteredAndSortedCountries = sortedCountries.filter((country) =>
        country.country_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        country.country_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        country.currency_code.toLowerCase().includes(searchTerm.toLowerCase())
    )
    const data = filteredAndSortedCountries
        .map(row => ({
            id: row._id,
            country_name: row.country_name,
            country_code: row.country_code,
            currency_code: row.currency_code,
            created_by: row.created_by?.name || "N/A",
            created_at: formatDate(row.created_at),
            status: row.status.toUpperCase()
        }));

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            <div className="flex flex-col gap-4 md:flex-row md:gap-6">
                {userHasRequestPermission && (
                <div className="w-full md:w-1/3">
                    <Card>
                        <CardHeader>
                            <CardTitle>Country Management Request</CardTitle>
                            <CardDescription>Add new Country</CardDescription>
                        </CardHeader>

                        <CardContent>
                            {showSuccessAlert && (
                                <Alert className="mt-4 bg-green-100 text-green-800 border-green-300">
                                    <CheckCircle2 className="h-4 w-4"/>
                                    <AlertTitle>Success</AlertTitle>
                                    <AlertDescription>
                                        Operation completed successfully.
                                    </AlertDescription>
                                </Alert>
                            )}
                            {showErrorAlert && (
                                <Alert className="mt-4 bg-red-100 text-red-800 border-red-300">
                                    <AlertTriangle className="h-4 w-4"/>
                                    <AlertTitle>Error</AlertTitle>
                                    <AlertDescription>
                                        {error}
                                    </AlertDescription>
                                </Alert>
                            )}
                            <div className='h-3'></div>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="space-y-2">
                                    <label htmlFor="country">Country</label>
                                    <CountrySelect onValueChange={handleCountryChange}/>
                                </div>

                                {error && <p className="text-red-600">{error}</p>}
                                <div className="flex justify-between">
                                    <Button type="button" variant="outline">
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={!!error || isLoading}>
                                        {isLoading ? (
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                                                Requesting...
                                            </>
                                        ) : (
                                            'Request'
                                        )}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                </div>
                )}
                {userHasViewPermission && (
                <div className="w-full md:w-2/3">
                    <Card>
                        <CardHeader>
                            <CardTitle>Saved Countries</CardTitle>
                            <CardDescription>View and manage your saved Countries</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="mb-4">
                                <Input
                                    placeholder="Search countries..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <Tabs aria-label="Tabs with underline" variant="underline">
                                <Tabs.Item active title="Approved">
                                    <DataExporter
                                        data={data}
                                        filename="country"
                                        title="Country Report"
                                    />
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                                                                <TableHead className="w-[100px]">ID</TableHead>
                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_name')}>
                                                    Country Name <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>

                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_code')}>
                                                    Country Code <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>

                                                <TableHead className="cursor-pointer">
                                                    Country ID <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>


                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('created_at')}>
                                                    Created At <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>
                                                <TableHead>Active</TableHead>
                                                <TableHead>Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {isFetching ? (
                                                <TableRow>
                                                    <TableCell colSpan={7} className="text-center">
                                                        <Loader2 className="h-6 w-6 animate-spin mx-auto"/>
                                                        <span>Loading countries...</span>
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                filteredAndSortedCountries.filter((type) => type.status.toLowerCase() === "active").map((country, index) => (
                                                    <TableRow key={country._id}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>{country.country_name}</TableCell>

                                                        <TableCell>{country.country_code}</TableCell>
                                                        <TableCell>
                                                            {countries.find((c) => c.code.toUpperCase() === country.country_code.toUpperCase())?.isoNumeric || 'N/A'}
                                                        </TableCell>
                                                        <TableCell>{formatDate(country.created_at)}</TableCell>
                                                        <TableCell>
                                                            <Switch
                                                                checked={country.is_active}
                                                                onCheckedChange={() => handleToggleStatus(country._id, country.is_active)}
                                                                disabled={isLoading}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <AlertDialog>
                                                                <AlertDialogTrigger asChild>
                                                                    <Button
                                                                        variant="destructive"
                                                                        size="sm"
                                                                        disabled={isLoading}
                                                                    >
                                                                        <TrashIcon className="h-4 w-4"/>
                                                                    </Button>
                                                                </AlertDialogTrigger>
                                                                <AlertDialogContent>
                                                                    <AlertDialogHeader>
                                                                        <AlertDialogTitle>Delete
                                                                            Country</AlertDialogTitle>
                                                                        <AlertDialogDescription>
                                                                            Are you sure you want to
                                                                            delete {country.country_name}? This action
                                                                            cannot be undone.
                                                                        </AlertDialogDescription>
                                                                    </AlertDialogHeader>
                                                                    <AlertDialogFooter>
                                                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                                        <AlertDialogAction
                                                                            onClick={() => handleDelete(country)}
                                                                            className="bg-red-600 hover:bg-red-700"
                                                                        >
                                                                            {isLoading ? (
                                                                                <>
                                                                                    <Loader2
                                                                                        className="mr-2 h-4 w-4 animate-spin"/>
                                                                                    Deleting...
                                                                                </>
                                                                            ) : (
                                                                                'Delete'
                                                                            )}
                                                                        </AlertDialogAction>
                                                                    </AlertDialogFooter>
                                                                </AlertDialogContent>
                                                            </AlertDialog>
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                            )}
                                        </TableBody>
                                    </Table>
                                </Tabs.Item>
                                <Tabs.Item  title="Pending">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_name')}>
                                                    Country Name <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>
                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_name')}>
                                                    Country Name <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>
                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_code')}>
                                                    Country Code <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>

                                                <TableHead className="cursor-pointer">
                                                    Country ID <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>

                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('created_at')}>
                                                    Created At <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>
                                                <TableHead>Active</TableHead>
                                                <TableHead>Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {isFetching ? (
                                                <TableRow>
                                                    <TableCell colSpan={7} className="text-center">
                                                        <Loader2 className="h-6 w-6 animate-spin mx-auto"/>
                                                        <span>Loading Countries ...</span>
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                filteredAndSortedCountries.filter((type) =>  type.status.toLowerCase() === "pending").map((country, index) => (
                                                    <TableRow key={country._id}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>{country.country_name}</TableCell>
                                                        <TableCell>{country.country_name}</TableCell>
                                                        <TableCell>{country.country_code}</TableCell>
                                                        <TableCell>
                                                            {countries.find((c) => c.code.toUpperCase() === country.country_code.toUpperCase())?.isoNumeric || 'N/A'}
                                                        </TableCell>
                                                        <TableCell>{formatDate(country.created_at)}</TableCell>
                                                        <TableCell>
                                                            <Switch
                                                                checked={country.is_active}
                                                                onCheckedChange={() => handleToggleStatus(country._id, country.is_active)}
                                                                disabled={isLoading}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <ActionsButtonGroup
                                                                entity={country}
                                                                entityType="countries" // For BIN Type
                                                                entityName={country.country_code}
                                                                userHasApprovePermission={userHasApprovePermission}
                                                                userHasDeletePermission={userHasViewPermission}
                                                                handleDeleteClick={handleDelete}
                                                                fetchEntities={fetchCountries}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                            )}
                                        </TableBody>
                                    </Table>
                                </Tabs.Item>

                                <Tabs.Item  title="Modify">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                                                                <TableHead className="w-[100px]">ID</TableHead>
<TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_name')}>
                                                    Country Name <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>
                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_code')}>
                                                    Country Code <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>

                                                <TableHead className="cursor-pointer">
                                                    Country ID <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>


                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('created_at')}>
                                                    Created At <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>
                                                <TableHead>Active</TableHead>
                                                <TableHead>Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {isFetching ? (
                                                <TableRow>
                                                    <TableCell colSpan={7} className="text-center">
                                                        <Loader2 className="h-6 w-6 animate-spin mx-auto"/>
                                                        <span>Loading Countries ...</span>
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                filteredAndSortedCountries.filter((type) =>  type.status.toLowerCase() === "modify").map((country, index) => (
                                                    <TableRow key={country._id}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>{country.country_name}</TableCell>
                                                        <TableCell>{country.country_code}</TableCell>
                                                        <TableCell>
                                                            {countries.find((c) => c.code.toUpperCase() === country.country_code.toUpperCase())?.isoNumeric || 'N/A'}
                                                        </TableCell>
                                                        <TableCell>{formatDate(country.created_at)}</TableCell>
                                                        <TableCell>
                                                            <Switch
                                                                checked={country.is_active}
                                                                onCheckedChange={() => handleToggleStatus(country._id, country.is_active)}
                                                                disabled={isLoading}
                                                            />
                                                        </TableCell>
                                                        <TableCell>{country.status.toUpperCase()}
                                                            <br/> Notes: {country.reason}</TableCell>
                                                        <TableCell>
                                                            <ActionsButtonGroup
                                                                entity={country}
                                                                entityType="countries" // For BIN Type
                                                                entityName={country.country_code}
                                                                userHasApprovePermission={userHasApprovePermission}
                                                                userHasDeletePermission={userHasViewPermission}
                                                                handleDeleteClick={(v)=>handleDelete(v)}
                                                                fetchEntities={fetchCountries}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                            )}
                                        </TableBody>
                                    </Table>
                                </Tabs.Item>

                                <Tabs.Item  title="Declined">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                                                                <TableHead className="w-[100px]">ID</TableHead>
<TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_name')}>
                                                    Country Name <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>
                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('country_code')}>
                                                    Country Code <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>

                                                <TableHead className="cursor-pointer">
                                                    Country ID <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>

                                                <TableHead className="cursor-pointer"
                                                           onClick={() => handleSort('created_at')}>
                                                    Created At <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                                </TableHead>
                                                <TableHead>Active</TableHead>
                                                <TableHead>Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {isFetching ? (
                                                <TableRow>
                                                    <TableCell colSpan={7} className="text-center">
                                                        <Loader2 className="h-6 w-6 animate-spin mx-auto"/>
                                                        <span>Loading Countries ...</span>
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                filteredAndSortedCountries.filter((type) => type.status.toLowerCase() === "decline").map((country, index) => (
                                                    <TableRow key={country._id}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>{country.country_name}</TableCell>
                                                        <TableCell>{country.country_code}</TableCell>
                                                        <TableCell>
                                                            {countries.find((c) => c.code.toUpperCase() === country.country_code.toUpperCase())?.isoNumeric || 'N/A'}
                                                        </TableCell>
                                                        <TableCell>{formatDate(country.created_at)}</TableCell>
                                                        <TableCell>
                                                            <Switch
                                                                checked={country.is_active}
                                                                onCheckedChange={() => handleToggleStatus(country._id, country.is_active)}
                                                                disabled={isLoading}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            {country.status.toUpperCase()} <br/> Notes: {country.reason}
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                            )}
                                        </TableBody>
                                    </Table>
                                </Tabs.Item>

                            </Tabs>

                        </CardContent>
                    </Card>
                </div>
                )}
            </div>
            <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Submission</AlertDialogTitle>
                        <AlertDialogDescription>
                            Do you want to submit this record? This record will be pending for approval!
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmSubmit}>Submit</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}