//@ts-nocheck

"use client"


import type React from "react"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Tabs } from "flowbite-react"

import { Sheet, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"

import axios from "@/utils/axiosInstance"
import { ArrowUpDown, Trash2 } from "lucide-react"
import { formatDate } from "@/utils/helpers"
import { useAppSelector } from "@/store/hooks"
import { ActionsButtonGroup } from "@/components/ActionButtons"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { Alert } from "@/components/alert"

import InputMask from "react-input-mask"
import DataExporter from "@/components/DataExporter";

interface BinType {
    _id: string
    type: string
    cardScheme: string
    binVariant: string
    binCategory: string
    programmeType: string
    reason: string
    created_at: string
    created_by: string
    status: string
}

interface BinRange {
    _id: number
    binType: string
    binCode: string
    binCodePrefix: string
    binCodeSuffix: string
    currency: string
    bin_start: string
    bin_end: string
    created_at: string
    created_by: any
    status: string
    category?: string // Keep for backward compatibility
}

interface Message {
    type: "success" | "error"
    content: string
}

interface Role {
    permissions: string[]
}

interface Currency {
    _id: string
    currency_code: string
    is_active: boolean
    status: string
}

export default function BinRange() {
    const [formData, setFormData] = useState({
        binType: "",
        binCode: "",
        binCodePrefix: "",
        binCodeSuffix: "",
        currency: "",
        bin_end: "",
        bin_start: "",
        created_by: localStorage.getItem("user"),
    })
    const [savedBinRanges, setSavedBinRanges] = useState<BinRange[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<{ key: keyof BinRange; direction: "asc" | "desc" } | null>(null)
    const [error, setError] = useState<string>("")
    const [deleteId, setDeleteId] = useState<number | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [alert, setAlert] = useState<{ message: string; type: "success" | "error" } | null>(null)
    const [isOverlayVisible, setIsOverlayVisible] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [isSheetOpen, setIsSheetOpen] = useState(false)
    const [savedbinTypes, setSavedbinTypes] = useState<BinType[]>([])
    const [currencies, setCurrencies] = useState<Currency[]>([])
    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
        fetchBinRanges()
    }, [user.roles])

    useEffect(() => {
        fetchInitialData()
    }, [])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Card Programme Type_Request")
    const userHasDeletePermission = hasPermission(roles, "Card Programme Type_Delete")
    const userHasApprovePermission = hasPermission(roles, "Card Programme Type_Approve")
    const userHasViewPermission = hasPermission(roles, "Card Programme Type_View")

    const getBinTypeName = (binTypeId: string) => {
        const binType = savedbinTypes.find((type) => type._id === binTypeId)
        return binType ? binType.type : "N/A"
    }

    const getCurrencyCode = (currencyId: string) => {
        const currency = currencies.find((ccy) => ccy._id === currencyId)
        return currency ? currency.currency_code : "N/A"
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)
        try {
            await axios.post("/bin-range", formData)
            await fetchBinRanges()
            setFormData({
                binType: "",
                binCode: "",
                binCodePrefix: "",
                binCodeSuffix: "",
                currency: "",
                bin_end: "",
                bin_start: "",
                created_by: localStorage.getItem("user"),
            })
            setAlert({ message: " Range created successfully!", type: "success" })
            setIsSheetOpen(false)
        } catch (error) {
            console.error("Error creating  Range", error)
            setAlert({ message: "Error creating Bin Range. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const fetchInitialData = async () => {
        setIsLoading(true)
        try {
            const [binTypes, cur] = await Promise.all([axios.get("/bin-types"), axios.get("product-currencies")])

            setSavedbinTypes(binTypes.data.data)
            setCurrencies(cur.data)
        } catch (error) {
            console.error("Error fetching BIN Types", error)
            setAlert({ message: "Error fetching data. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const fetchBinRanges = async () => {
        setIsLoading(true)
        try {
            const response = await axios.get("/bin-range")
            setSavedBinRanges(response.data.data)
        } catch (error) {
            console.error("Error fetching Bin Range", error)
            setAlert({ message: "Error fetching data. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }


    const handleSort = (key: keyof BinRange) => {
        let direction: "asc" | "desc" = "asc"
        if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc"
        }
        setSortConfig({ key, direction })
    }

    const filteredAndSortedRanges = savedBinRanges
        .filter((range) => {
            const searchFields = [
                range.binCode,
                getBinTypeName(range.binType),
                getCurrencyCode(range.currency),
                range.bin_start,
                range.bin_end,
            ].map((field) => field?.toLowerCase() || "")

            return searchFields.some((field) => field.includes(searchTerm.toLowerCase()))
        })
        .sort((a, b) => {
            if (sortConfig === null) {
                return 0
            }
            const { key, direction } = sortConfig

            // Handle special cases for binType and currency which need to be displayed as names
            if (key === "binType") {
                const aName = getBinTypeName(a[key])
                const bName = getBinTypeName(b[key])
                return direction === "asc" ? aName.localeCompare(bName) : bName.localeCompare(aName)
            }

            if (key === "currency") {
                const aCode = getCurrencyCode(a[key])
                const bCode = getCurrencyCode(b[key])
                return direction === "asc" ? aCode.localeCompare(bCode) : bCode.localeCompare(aCode)
            }

            if (a[key] < b[key]) {
                return direction === "asc" ? -1 : 1
            }
            if (a[key] > b[key]) {
                return direction === "asc" ? 1 : -1
            }
            return 0
        })

    const handleDelete = async () => {
        if (deleteId === null) return

        setIsLoading(true)
        setIsOverlayVisible(true)
        try {
            await axios.delete(`/bin-ranges/${deleteId}`)
            setSavedBinRanges((prev) => prev.filter((range) => range._id !== deleteId))
            setDeleteId(null)
            await fetchBinRanges()
            setAlert({ message: "Bin Range deleted successfully!", type: "success" })
        } catch (error) {
            console.error("Error deleting Bin Range", error)
            setAlert({ message: "Error deleting Bin Range. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
            setIsOverlayVisible(false)
            setDeleteModalOpen(false)
        }
    }

    const handleDeleteClick = (id: string) => {
        setDeleteId(Number.parseInt(id))
        setDeleteModalOpen(true)
    }

    const renderTable = (status: string) => {
        const data = savedBinRanges
            .filter(row => row.status.toLowerCase() === status.toLowerCase()) // Filtering by status
            .map(row => ({
                id: row._id,
                programme_type:  `${row.binType.type} ${row.currency.currency_code}`,
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase()
            }));
        return (
            <>
                <DataExporter
                    data={data}
                    filename="card-programme-type"
                    title="Card Programme Type Report"
                />
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[100px]">ID</TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("binType")}>
                            Card Programme Type <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                        </TableHead>
                        <TableHead className="cursor-pointer"  >
                            BIN Code
                        </TableHead>

                        <TableHead className="cursor-pointer"  >
                           Currency
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("created_by")}>
                            Created By <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("created_at")}>
                            Created At <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                        </TableHead>
                        <TableHead>Status</TableHead>

                    </TableRow>
                </TableHeader>
                <TableBody>
                    {filteredAndSortedRanges
                        .filter((range) => range.status.toLowerCase() === status)
                        .map((range, index) => (
                            <TableRow key={range._id}>
                                <TableCell>{index + 1}</TableCell>
                                <TableCell>{range.binType.type} {range.currency.currency_code}</TableCell>
                                <TableCell>{range.binCode}</TableCell>
                                <TableCell>{range.currency.currency_code}</TableCell>

                                <TableCell>{range.created_by?.name || "N/A"}</TableCell>
                                <TableCell>{formatDate(range.created_at)}</TableCell>
                                <TableCell>{range.status.toUpperCase()}</TableCell>

                            </TableRow>
                        ))}
                </TableBody>
            </Table></>
        )
    }

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            {isLoading && <LoadingOverlay />}
            {isOverlayVisible && <div className="fixed inset-0 bg-black bg-opacity-50 z-50" />}
            <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>Card Programme Type</CardTitle>
                        <CardDescription>View Card Programme Types</CardDescription>
                    </div>

                </CardHeader>
                <CardContent>
                    {alert && <Alert message={alert.message} type={alert.type} onClose={() => setAlert(null)} />}

                    {userHasViewPermission && (
                        <>
                            <div className="mb-4">
                                <Input
                                    placeholder="Search Card Programme Type..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>

                            <Tabs aria-label="Tabs with underline" variant="underline">
                                <Tabs.Item active title="Approved">
                                    {renderTable("active")}
                                </Tabs.Item>

                            </Tabs>
                        </>
                    )}
                </CardContent>
            </Card>

            <AlertDialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this Card Programme Type? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}

