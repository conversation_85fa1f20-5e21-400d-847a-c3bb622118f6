// @ts-nocheck
"use client"

import Image from "next/image"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import axiosInstance from "@/utils/axiosInstance"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { TrendingUp, CreditCard, Users, BarChart3, PieChartIcon, Activity, Target, X } from "lucide-react"
import { <PERSON><PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const asset = process.env.NEXT_PUBLIC_ASSET_URL;
interface BinAllocation {
    _id: string
    cardRange: string
    binStart: string
    binEnd: string
    total_usage: number
    cards: any[]
    product_version: {
        _id: string
        version_name: string
        version_code: string
    }
    company: any
    programme: any
    createdAt: string
}

interface CardImage {
    _id: string
    front_side: string
    back_side: string
    product_version: {
        _id: string
        version_name: string
    }
    company: {
        _id: string
    }
}

interface ProductVersion {
    _id: string
    version_name: string
    version_code: string
}

export default function Page({ params }: { params: { cip: string; company: string } }) {
    const [companyData, setCompanyData] = useState<null | any>(null)
    const [programmes, setCip] = useState<null | any>(null)
    const [currencies, setCurrencies] = useState([])
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [binAllocations, setBinAllocations] = useState<any[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [sortConfig, setSortConfig] = useState({ key: null, direction: "ascending" })
    const [savedBinCategories, setSavedBinCategories] = useState([])
    const [savedBinVariants, setSavedBinCVariants] = useState([])
    const [isLoading, setIsLoading] = useState(false)
    const [selectedProgram, setSelectedProgram] = useState<any | null>(null)
    const [isSheetOpen, setIsSheetOpen] = useState(false)
    const [selectedImage, setSelectedImage] = useState<string | null>(null)
    const router = useRouter()

    // Calculate allocation statistics using actual card usage data
    const calculateAllocationStats = (binStart: string, binEnd: string, totalUsage = 0, cardsArray: any[] = []) => {
        const startNum = Number.parseInt(binStart.replace(/\D/g, ""))
        const endNum = Number.parseInt(binEnd.replace(/\D/g, ""))
        const totalAllocation = endNum - startNum + 1

        // Use actual card count from the cards array or total_usage field
        const actualIssued = Math.max(totalUsage, cardsArray.length)
        const remaining = Math.max(0, totalAllocation - actualIssued)
        const utilizationRate = totalAllocation > 0 ? (actualIssued / totalAllocation) * 100 : 0

        return {
            totalAllocation,
            issued: actualIssued,
            remaining,
            utilizationRate,
        }
    }

    // Filter product versions that have BIN allocations
    const getProductVersionsWithAllocations = () => {
        if (!programmes?.[0]?.productVersionName) return []
        return programmes[0].productVersionName.filter((version: ProductVersion) => {
            return binAllocations.some(
                (allocation) =>
                    allocation.product_version._id === version._id ||
                    allocation.product_version.version_name.toLowerCase().includes(version.version_name.toLowerCase()),
            )
        })
    }

    async function fetchCompanyDetails() {
        try {
            const [companyResponse, imagesResponse] = await Promise.all([
                axiosInstance.get(`/company/${params.company}`),
                axiosInstance.get("/images"),
            ])

            setCompanyData(companyResponse.data.company)
            setCip(companyResponse.data.cip.filter((r) => r._id === params.cip))
            const companyImages = imagesResponse.data.filter((image: any) => image.company._id === process.env.NEXT_PUBLIC_DEFAULT_COMPANY_ID)
            setCardImages(companyImages)

            try {
                const binResponse = await axiosInstance.get(`/bin/cip/${params.cip}`)
                setBinAllocations(binResponse.data || [])
            } catch (binError) {
                console.error("Error fetching BIN allocations:", binError)
                setBinAllocations([])
            }
        } catch (error: any) {
            console.error("Error fetching company details:", error)
            setError(error.response?.data.message || "Failed to fetch company data")
            setBinAllocations([])
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchCompanyDetails()
    }, [params.company])

    useEffect(() => {
        fetchCardTypes()
    }, [])

    const fetchCardTypes = async () => {
        setIsLoading(true)
        try {
            const [categories, variants, currencies] = await Promise.all([
                axiosInstance.get("/bin-category"),
                axiosInstance.get("/bin-variant"),
                axiosInstance.get("product-currencies"),
            ])
            setSavedBinCategories(categories.data)
            setSavedBinCVariants(variants.data)
            setCurrencies(currencies.data)
        } catch (error) {
            console.error("Error fetching BIN Types", error)
        } finally {
            setIsLoading(false)
        }
    }

    const handleImageClick = (imageSrc: string) => {
        setSelectedImage(imageSrc)
    }

    const handleClosePopup = () => {
        setSelectedImage(null)
    }

    if (loading) {
        return <LoadingOverlay />
    }

    if (!companyData) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-primary-50 to-indigo-50 flex items-center justify-center p-4">
                <Card className="w-full max-w-md mx-auto shadow-xl">
                    <CardContent className="py-16 text-center">
                        <div className="mb-4">
                            <CreditCard className="h-16 w-16 text-slate-400 mx-auto" />
                        </div>
                        <p className="text-xl font-semibold text-slate-600 mb-2">No Data Found</p>
                        <p className="text-sm text-slate-500">Unable to load programme details</p>
                    </CardContent>
                </Card>
            </div>
        )
    }

    // Calculate total statistics using actual card usage data
    const totalStats = binAllocations.reduce(
        (acc, allocation) => {
            const stats = calculateAllocationStats(
                allocation.binStart,
                allocation.binEnd,
                allocation.total_usage || 0,
                allocation.cards || [],
            )
            return {
                totalAllocation: acc.totalAllocation + stats.totalAllocation,
                totalIssued: acc.totalIssued + stats.issued,
                totalRemaining: acc.totalRemaining + stats.remaining,
            }
        },
        { totalAllocation: 0, totalIssued: 0, totalRemaining: 0 },
    )

    const allocatedProductVersions = getProductVersionsWithAllocations()

    // If no allocations exist, show empty state
    if (binAllocations.length === 0 || allocatedProductVersions.length === 0) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-primary-50 to-indigo-50">
                <div className="container mx-auto p-4 sm:p-6 lg:p-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="bg-white rounded-2xl shadow-xl border border-slate-200 overflow-hidden">
                            <div className="bg-gradient-to-r from-primary-600 via-primary-700 to-indigo-700 px-6 sm:px-8 py-6">
                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                                    <div className="bg-white/20 p-3 rounded-xl">
                                        <Activity className="h-8 w-8 text-white" />
                                    </div>
                                    <div className="flex-1">
                                        <h1 className="text-2xl sm:text-3xl font-bold text-white">Programme Dashboard</h1>
                                        <p className="text-primary-100 mt-1 text-sm sm:text-base">
                                            {programmes?.[0]?.programManagerType?.manager_type || "Standard Manager"}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/* Empty State */}
                    <div className="flex items-center justify-center min-h-[400px]">
                        <Card className="w-full max-w-lg mx-auto shadow-xl">
                            <CardContent className="py-16 text-center">
                                <div className="mb-6">
                                    <Target className="h-20 w-20 text-slate-300 mx-auto" />
                                </div>
                                <h3 className="text-2xl font-semibold text-slate-700 mb-3">No BIN Allocations Found</h3>
                                <p className="text-slate-500 mb-6">
                                    There are currently no BIN allocations for this programme. Allocate BIN blocks to product versions to
                                    see detailed analytics.
                                </p>
                                <button
                                    onClick={() => router.back()}
                                    className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                                >
                                    Go Back
                                </button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-primary-50 to-indigo-50">
            <div className="container mx-auto p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
                {/* Header Section */}
                <div className="bg-white rounded-2xl shadow-xl border border-slate-200 overflow-hidden">
                    <div className="bg-gradient-to-r from-primary-600 via-primary-700 to-indigo-700 px-6 sm:px-8 py-6">
                        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                            <div className="bg-white/20 p-3 rounded-xl">
                                <Activity className="h-8 w-8 text-white" />
                            </div>
                            <div className="flex-1">
                                <h1 className="text-2xl sm:text-3xl font-bold text-white">Programme Dashboard</h1>
                                <p className="text-primary-100 mt-1 text-sm sm:text-base">
                                    {programmes?.[0]?.programManagerType?.manager_type || "Standard Manager"}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Summary Statistics Cards */}
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-primary-50 to-primary-100">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex items-center justify-between">
                                <div className="min-w-0 flex-1">
                                    <p className="text-xs sm:text-sm font-medium text-primary-700 truncate">Total Allocation</p>
                                    <p className="text-lg sm:text-2xl font-bold text-primary-800 truncate">
                                        {totalStats.totalAllocation.toLocaleString()}
                                    </p>
                                </div>
                                <div className="bg-primary-200 p-2 sm:p-3 rounded-full">
                                    <CreditCard className="h-4 w-4 sm:h-6 sm:w-6 text-primary-700" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-green-100">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex items-center justify-between">
                                <div className="min-w-0 flex-1">
                                    <p className="text-xs sm:text-sm font-medium text-green-700 truncate">Total Issued</p>
                                    <p className="text-lg sm:text-2xl font-bold text-green-800 truncate">
                                        {totalStats.totalIssued.toLocaleString()}
                                    </p>
                                </div>
                                <div className="bg-green-200 p-2 sm:p-3 rounded-full">
                                    <TrendingUp className="h-4 w-4 sm:h-6 sm:w-6 text-green-700" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-teal-50 to-teal-100">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex items-center justify-between">
                                <div className="min-w-0 flex-1">
                                    <p className="text-xs sm:text-sm font-medium text-teal-700 truncate">Remaining</p>
                                    <p className="text-lg sm:text-2xl font-bold text-teal-800 truncate">
                                        {totalStats.totalRemaining.toLocaleString()}
                                    </p>
                                </div>
                                <div className="bg-teal-200 p-2 sm:p-3 rounded-full">
                                    <Users className="h-4 w-4 sm:h-6 sm:w-6 text-teal-700" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-purple-50 to-purple-100">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex items-center justify-between">
                                <div className="min-w-0 flex-1">
                                    <p className="text-xs sm:text-sm font-medium text-purple-700 truncate">Utilization</p>
                                    <p className="text-lg sm:text-2xl font-bold text-purple-800 truncate">
                                        {totalStats.totalAllocation > 0
                                            ? ((totalStats.totalIssued / totalStats.totalAllocation) * 100).toFixed(1)
                                            : 0}
                                        %
                                    </p>
                                </div>
                                <div className="bg-purple-200 p-2 sm:p-3 rounded-full">
                                    <Target className="h-4 w-4 sm:h-6 sm:w-6 text-purple-700" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Analytics Charts */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
                    {/* Allocation Distribution Pie Chart */}
                    <Card className="shadow-xl border-0">
                        <CardHeader className="pb-4">
                            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                                <div className="bg-primary-100 p-2 rounded-lg">
                                    <PieChartIcon className="h-5 w-5 text-primary-600" />
                                </div>
                                Allocation Distribution
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <ChartContainer
                                config={{
                                    allocation: {
                                        label: "Allocation",
                                        color: "hsl(var(--chart-1))",
                                    },
                                }}
                                className="h-[250px] sm:h-[300px] lg:h-[350px]"
                            >
                                <ResponsiveContainer width="100%" height="100%">
                                    <PieChart>
                                        <Pie
                                            data={binAllocations.map((allocation, index) => {
                                                const stats = calculateAllocationStats(
                                                    allocation.binStart,
                                                    allocation.binEnd,
                                                    allocation.total_usage || 0,
                                                    allocation.cards || [],
                                                )
                                                return {
                                                    name: allocation.product_version.version_code,
                                                    value: stats.totalAllocation,
                                                    issued: stats.issued,
                                                    fill: `hsl(${(index * 137.5) % 360}, 70%, 50%)`,
                                                }
                                            })}
                                            cx="50%"
                                            cy="50%"
                                            labelLine={false}
                                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                            outerRadius="80%"
                                            fill="#8884d8"
                                            dataKey="value"
                                        >
                                            {binAllocations.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={`hsl(${(index * 137.5) % 360}, 70%, 50%)`} />
                                            ))}
                                        </Pie>
                                        <ChartTooltip content={<ChartTooltipContent />} />
                                    </PieChart>
                                </ResponsiveContainer>
                            </ChartContainer>
                        </CardContent>
                    </Card>

                    {/* Utilization Rates Bar Chart */}
                    <Card className="shadow-xl border-0">
                        <CardHeader className="pb-4">
                            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                                <div className="bg-green-100 p-2 rounded-lg">
                                    <BarChart3 className="h-5 w-5 text-green-600" />
                                </div>
                                Card Utilization Analysis
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <ChartContainer
                                config={{
                                    issued: {
                                        label: "Issued Cards",
                                        color: "hsl(var(--chart-2))",
                                    },
                                    remaining: {
                                        label: "Remaining Cards",
                                        color: "hsl(var(--chart-3))",
                                    },
                                }}
                                className="h-[250px] sm:h-[300px] lg:h-[350px]"
                            >
                                <ResponsiveContainer width="100%" height="100%">
                                    <BarChart
                                        data={binAllocations.map((allocation) => {
                                            const stats = calculateAllocationStats(
                                                allocation.binStart,
                                                allocation.binEnd,
                                                allocation.total_usage || 0,
                                                allocation.cards || [],
                                            )
                                            return {
                                                name: allocation.product_version.version_code,
                                                issued: stats.issued,
                                                remaining: stats.remaining,
                                                total: stats.totalAllocation,
                                            }
                                        })}
                                        margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                                    >
                                        <CartesianGrid strokeDasharray="3 3" />
                                        <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} fontSize={12} interval={0} />
                                        <YAxis fontSize={12} />
                                        <ChartTooltip content={<ChartTooltipContent />} />
                                        <Legend />
                                        <Bar
                                            dataKey="issued"
                                            stackId="a"
                                            fill="var(--color-issued)"
                                            name="Issued Cards"
                                            radius={[0, 0, 0, 0]}
                                        />
                                        <Bar
                                            dataKey="remaining"
                                            stackId="a"
                                            fill="var(--color-remaining)"
                                            name="Remaining Cards"
                                            radius={[4, 4, 0, 0]}
                                        />
                                    </BarChart>
                                </ResponsiveContainer>
                            </ChartContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Card Allocation Overview */}
                <Card className="shadow-xl border-0">
                    <CardHeader className="pb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                            <div>
                                <CardTitle className="text-xl sm:text-2xl font-bold text-slate-800">Card Allocation Overview</CardTitle>
                                <p className="text-sm text-slate-600 mt-1">Detailed breakdown of all active product allocations</p>
                            </div>
                            <Badge variant="outline" className="text-sm w-fit bg-primary-50 text-primary-700 border-primary-200">
                                {allocatedProductVersions.length} Active Product{allocatedProductVersions.length !== 1 ? "s" : ""}
                            </Badge>
                        </div>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Product Cards */}
                        <div className="space-y-6">
                            {allocatedProductVersions.map((v: ProductVersion) => {
                                const binAllocation = binAllocations.find(
                                    (allocation) =>
                                        allocation.product_version._id === v._id ||
                                        allocation.product_version.version_name.toLowerCase().includes(v.version_name.toLowerCase()),
                                )
                                const stats = binAllocation
                                    ? calculateAllocationStats(
                                        binAllocation.binStart,
                                        binAllocation.binEnd,
                                        binAllocation.total_usage || 0,
                                        binAllocation.cards || [],
                                    )
                                    : { totalAllocation: 0, issued: 0, remaining: 0, utilizationRate: 0 }

                                const cardImage = cardImages.find((img: any) => img.product_version._id === v._id)
                                const frontImageUrl = cardImage?.front_side ? `${asset}${cardImage.front_side}` : undefined
                                const backImageUrl = cardImage?.back_side ? `${asset}${cardImage.back_side}` : undefined

                                return (
                                    <Card
                                        key={v._id}
                                        className="hover:shadow-lg transition-all duration-300 border border-slate-200 bg-gradient-to-r from-white to-slate-50"
                                    >
                                        <CardContent className="p-0">
                                            {/* Header Section */}
                                            <div className="bg-gradient-to-r from-slate-100 to-slate-50 px-6 py-4 border-b border-slate-200">
                                                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                                                    <div className="flex items-center gap-4 flex-1">
                                                        <div className="bg-white p-2 rounded-lg shadow-sm">
                                                            <CreditCard className="h-6 w-6 text-slate-600" />
                                                        </div>
                                                        <div>
                                                            <h3 className="text-lg sm:text-xl font-bold text-slate-900">{v.version_name}</h3>
                                                            <div className="flex items-center gap-2 mt-1">
                                                                <Badge
                                                                    variant={v.version_name.toLowerCase().includes("virtual") ? "secondary" : "default"}
                                                                    className={
                                                                        v.version_name.toLowerCase().includes("virtual")
                                                                            ? "bg-purple-100 text-purple-800 hover:bg-purple-100"
                                                                            : "bg-primary-100 text-primary-800 hover:bg-primary-100"
                                                                    }
                                                                >
                                                                    {v.version_name.toLowerCase().includes("virtual") ? "Virtual Card" : "Physical Card"}
                                                                </Badge>
                                                                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                                                    Active
                                                                </Badge>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {/* Utilization Circle - Desktop */}
                                                    <div className="hidden sm:flex items-center justify-center">
                                                        <div className="relative w-16 h-16">
                                                            <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                                                <path
                                                                    className="text-slate-200"
                                                                    stroke="currentColor"
                                                                    strokeWidth="3"
                                                                    fill="transparent"
                                                                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                                                />
                                                                <path
                                                                    className={
                                                                        stats.utilizationRate > 75
                                                                            ? "text-red-500"
                                                                            : stats.utilizationRate > 50
                                                                                ? "text-yellow-500"
                                                                                : "text-green-500"
                                                                    }
                                                                    stroke="currentColor"
                                                                    strokeWidth="3"
                                                                    strokeLinecap="round"
                                                                    fill="transparent"
                                                                    strokeDasharray={`${stats.utilizationRate}, 100`}
                                                                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                                                />
                                                            </svg>
                                                            <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-xs font-bold text-slate-700">
                                  {stats.utilizationRate.toFixed(0)}%
                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Content Section */}
                                            <div className="p-6">
                                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                                    {/* Card Images Section */}
                                                    <div className="lg:col-span-1">
                                                        <h4 className="text-sm font-semibold text-slate-700 mb-3 flex items-center gap-2">
                                                            <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                                                            Card Designs
                                                        </h4>
                                                        <div className="flex flex-wrap gap-3">
                                                            {frontImageUrl && (
                                                                <div className="relative group">
                                                                    <Image
                                                                        unoptimized
                                                                        src={frontImageUrl || "/placeholder.svg"}
                                                                        alt={`${v.version_name} front`}
                                                                        width={120}
                                                                        height={75}
                                                                        className="rounded-lg border-2 border-slate-200 cursor-pointer hover:border-primary-400 transition-all duration-200 shadow-sm hover:shadow-md"
                                                                        onClick={() => handleImageClick(frontImageUrl)}
                                                                    />
                                                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-all duration-200 flex items-center justify-center">
                                                                        <div className="opacity-0 group-hover:opacity-100 bg-white/90 px-2 py-1 rounded text-xs font-medium text-slate-700 transition-opacity">
                                                                            Front
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            )}
                                                            {backImageUrl && (
                                                                <div className="relative group">
                                                                    <Image
                                                                        unoptimized
                                                                        src={backImageUrl || "/placeholder.svg"}
                                                                        alt={`${v.version_name} back`}
                                                                        width={120}
                                                                        height={75}
                                                                        className="rounded-lg border-2 border-slate-200 cursor-pointer hover:border-primary-400 transition-all duration-200 shadow-sm hover:shadow-md"
                                                                        onClick={() => handleImageClick(backImageUrl)}
                                                                    />
                                                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-all duration-200 flex items-center justify-center">
                                                                        <div className="opacity-0 group-hover:opacity-100 bg-white/90 px-2 py-1 rounded text-xs font-medium text-slate-700 transition-opacity">
                                                                            Back
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            )}
                                                            {!cardImage && (
                                                                <div className="w-[120px] h-[75px] bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg border-2 border-dashed border-slate-300 flex flex-col items-center justify-center">
                                                                    <CreditCard className="h-6 w-6 text-slate-400 mb-1" />
                                                                    <span className="text-xs text-slate-500 font-medium">No Images</span>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    {/* Statistics Section */}
                                                    <div className="lg:col-span-2">
                                                        <h4 className="text-sm font-semibold text-slate-700 mb-4 flex items-center gap-2">
                                                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                                            Allocation Statistics
                                                        </h4>
                                                        {/* Stats Grid */}
                                                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
                                                            <div className="bg-gradient-to-br from-primary-50 to-primary-100 p-4 rounded-xl border border-primary-200">
                                                                <div className="flex items-center justify-between mb-2">
                                                                    <div className="bg-primary-200 p-1.5 rounded-lg">
                                                                        <Target className="h-4 w-4 text-primary-700" />
                                                                    </div>
                                                                </div>
                                                                <div className="text-2xl font-bold text-primary-800 mb-1">
                                                                    {stats.totalAllocation.toLocaleString()}
                                                                </div>
                                                                <div className="text-xs font-medium text-primary-600">Total Allocated</div>
                                                            </div>
                                                            <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200">
                                                                <div className="flex items-center justify-between mb-2">
                                                                    <div className="bg-green-200 p-1.5 rounded-lg">
                                                                        <TrendingUp className="h-4 w-4 text-green-700" />
                                                                    </div>
                                                                </div>
                                                                <div className="text-2xl font-bold text-green-800 mb-1">
                                                                    {stats.issued.toLocaleString()}
                                                                </div>
                                                                <div className="text-xs font-medium text-green-600">Cards Issued</div>
                                                            </div>
                                                            <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-4 rounded-xl border border-teal-200">
                                                                <div className="flex items-center justify-between mb-2">
                                                                    <div className="bg-teal-200 p-1.5 rounded-lg">
                                                                        <Users className="h-4 w-4 text-teal-700" />
                                                                    </div>
                                                                </div>
                                                                <div className="text-2xl font-bold text-teal-800 mb-1">
                                                                    {stats.remaining.toLocaleString()}
                                                                </div>
                                                                <div className="text-xs font-medium text-teal-600">Remaining</div>
                                                            </div>
                                                            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl border border-purple-200">
                                                                <div className="flex items-center justify-between mb-2">
                                                                    <div className="bg-purple-200 p-1.5 rounded-lg">
                                                                        <Activity className="h-4 w-4 text-purple-700" />
                                                                    </div>
                                                                </div>
                                                                <div className="text-2xl font-bold text-purple-800 mb-1">
                                                                    {stats.utilizationRate.toFixed(1)}%
                                                                </div>
                                                                <div className="text-xs font-medium text-purple-600">Utilization</div>
                                                            </div>
                                                        </div>

                                                        {/* Low Stock Alert */}
                                                        {stats.remaining < 1000 && (
                                                            <div className="mb-4 p-3 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
                                                                <div className="flex items-center gap-2">
                                                                    <div className="bg-amber-100 p-1.5 rounded-full">
                                                                        <svg
                                                                            className="h-4 w-4 text-amber-600"
                                                                            fill="none"
                                                                            viewBox="0 0 24 24"
                                                                            strokeWidth={2}
                                                                            stroke="currentColor"
                                                                        >
                                                                            <path
                                                                                strokeLinecap="round"
                                                                                strokeLinejoin="round"
                                                                                d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
                                                                            />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="flex-1">
                                                                        <h6 className="text-sm font-semibold text-amber-800">Low Stock Alert</h6>
                                                                        <p className="text-xs text-amber-700 mt-1">
                                                                            Only <span className="font-bold">{stats.remaining.toLocaleString()}</span> cards
                                                                            remaining for this product version. Consider allocating additional BIN ranges to
                                                                            avoid service interruption.
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {/* Progress Bar */}
                                                        <div className="mb-4">
                                                            <div className="flex justify-between items-center mb-2">
                                                                <span className="text-sm font-medium text-slate-700">Allocation Progress</span>
                                                                <span className="text-sm font-bold text-slate-900">
                                  {stats.utilizationRate.toFixed(1)}%
                                </span>
                                                            </div>
                                                            <div className="w-full bg-slate-200 rounded-full h-3 overflow-hidden">
                                                                <div
                                                                    className={`h-full rounded-full transition-all duration-500 ${
                                                                        stats.utilizationRate > 75
                                                                            ? "bg-gradient-to-r from-red-500 to-red-600"
                                                                            : stats.utilizationRate > 50
                                                                                ? "bg-gradient-to-r from-yellow-500 to-yellow-600"
                                                                                : "bg-gradient-to-r from-green-500 to-green-600"
                                                                    }`}
                                                                    style={{ width: `${Math.min(stats.utilizationRate, 100)}%` }}
                                                                ></div>
                                                            </div>
                                                        </div>

                                                        {/* BIN Details */}
                                                        {binAllocation && (
                                                            <div className="bg-slate-50 rounded-xl p-4 border border-slate-200">
                                                                <h5 className="text-sm font-semibold text-slate-700 mb-3 flex items-center gap-2">
                                                                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                                                    BIN Range Details
                                                                </h5>
                                                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                                                    <div>
                                                                        <div className="text-xs font-medium text-slate-600 mb-1">Card Range</div>
                                                                        <div className="font-mono text-sm bg-white px-3 py-2 rounded-lg border border-slate-200 font-semibold text-slate-800">
                                                                            {binAllocation.cardRange}
                                                                        </div>
                                                                    </div>
                                                                    <div>
                                                                        <div className="text-xs font-medium text-slate-600 mb-1">BIN Range</div>
                                                                        <div className="font-mono text-sm bg-white px-3 py-2 rounded-lg border border-slate-200 font-semibold text-slate-800">
                                                                            {binAllocation.binStart} - {binAllocation.binEnd}
                                                                        </div>
                                                                    </div>
                                                                    <div>
                                                                        <div className="text-xs font-medium text-slate-600 mb-1">Active Cards</div>
                                                                        <div className="font-mono text-sm bg-white px-3 py-2 rounded-lg border border-slate-200 font-semibold text-slate-800">
                                                                            {binAllocation.cards?.length || 0} cards
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )
                            })}
                        </div>
                    </CardContent>
                </Card>

                {/* Image Popup Modal */}
                {selectedImage && (
                    <div
                        className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
                        onClick={handleClosePopup}
                    >
                        <div className="relative max-w-4xl max-h-[90vh] w-full">
                            <Image
                                src={selectedImage || "/placeholder.svg"}
                                alt="Card Preview"
                                width={800}
                                height={500}
                                className="rounded-lg shadow-2xl w-full h-auto object-contain"
                                unoptimized
                            />
                            <button
                                onClick={handleClosePopup}
                                className="absolute top-4 right-4 bg-white/90 hover:bg-white rounded-full p-2 shadow-lg transition-colors"
                            >
                                <X className="h-5 w-5 text-slate-700" />
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}
