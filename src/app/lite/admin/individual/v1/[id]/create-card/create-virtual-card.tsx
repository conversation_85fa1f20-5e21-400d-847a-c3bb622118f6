//@ts-nocheck

"use client"

import { useEffect, useMemo, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"

import { useAppSelector } from "@/store/hooks"
import {alertHelper} from "@/utils/alertHelper";

interface CreateCardComponentProps {
    companyId: string
    onboarding: any
    account: any
    onApiSuccess: () => void
}

interface ProductVersion {
    _id: string
    version_code: string
    version_name: string
    status: string
    userType?: string
}

interface CorporateProduct {
    id: string
    code: string
    name: string
    description: string
    status: string
}

export default function CreateCardComponent({
                                                companyId,
                                                onboarding,
                                                account,
                                                onApiSuccess,
                                            }: CreateCardComponentProps) {
    const [dialogOpen, setDialogOpen] = useState(false)
    const [selectedOption, setSelectedOption] = useState("")
    const user = useAppSelector((state) => state.user.user)

    const [nickName, setNickName] = useState("")
    const [createCardLoading, setCreateCardLoading] = useState(false)
    const [dropdownLoading, setDropdownLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [companyData, setCompanyData] = useState<any>(null)
    const [cip, setCip] = useState<any[]>([])
    const [corporateProducts, setCorporateProducts] = useState<CorporateProduct[]>([])

    const isCorporateUser = user?.dashboard === "corporate" || user?.cardholder?.userType === "b2b"

    // Available products for regular users
    const availableProducts = useMemo(() => {
        return cip.flatMap((program) =>
            program.productVersionName.filter((r) => r.status === "active" && r.version_name.toLowerCase().includes("vtl")),
        )
    }, [cip])

    // Combined products for corporate users (from multiple APIs)
    const corporateAvailableProducts = useMemo(() => {
        console.log(corporateProducts)
        return corporateProducts.filter((product) => product.status === "active")
    }, [corporateProducts])

    useEffect(() => {
        if (availableProducts.length === 1 && !isCorporateUser) {
            setSelectedOption(availableProducts[0].version_code)
        }
    }, [availableProducts, isCorporateUser])

    useEffect(() => {
        if (dialogOpen) {
            const fetchData = async () => {
                setDropdownLoading(true)
                setError(null)

                try {
                    if (isCorporateUser) {
                        // Fetch corporate-specific products from multiple APIs
                        await fetchCorporateProducts()
                    } else {
                        // Fetch regular company data
                        const response = await axiosInstance.get(`/company/${companyId}`)
                        setCompanyData(response.data.company)
                        setCip(response.data.cip || [])
                    }
                } catch (err) {
                    setError("Failed to fetch product data")
                } finally {
                    setDropdownLoading(false)
                }
            }

            fetchData()
        }
    }, [dialogOpen, companyId, isCorporateUser])

    const fetchCorporateProducts = async () => {
        try {
            // Fetch from multiple APIs for corporate users
            const [ fetchProducts] = await Promise.allSettled([
                axiosInstance.get(`b2b/${user.recordId}/products`),
            ])

            const allProducts: CorporateProduct[] = []


            // Process corporate config products
            if (fetchProducts.status === "fulfilled") {
                const configProducts = fetchProducts.value.data.products.filter(v=>v.version_name.toLowerCase().includes("vtl") ) || []
                allProducts.push(
                    ...configProducts.map((p: any) => ({
                        id: p._id,
                        code: p.version_code,
                        name: p.version_name,
                        description: `${p.version_name} (${p.version_code})`,
                        status: p.status,
                    })),
                )
            }

            // Remove duplicates based on product code
            const uniqueProducts = allProducts.filter(
                (product, index, self) => index === self.findIndex((p) => p.code === product.code),
            )

            setCorporateProducts(uniqueProducts)

            // Auto-select if only one product available
            if (uniqueProducts.length === 1) {
                setSelectedOption(uniqueProducts[0].code)
            }
        } catch (err) {
            console.error("Error fetching corporate products:", err)
            // Fallback to default R004 if all APIs fail
            setCorporateProducts([
                {
                    id: "fallback-r004",
                    code: "R004",
                    name: "VISA STANDARD BUSINESS DEBIT EUR VTL",
                    description: "VISA STANDARD BUSINESS DEBIT EUR VTL (R004)",
                    status: "active",
                },
            ])
        }
    }

    const handleCreateCard = () => {
        setDialogOpen(true)
    }

    const handleDialogSubmit = async () => {
        if (!selectedOption) {
            setError("Please select an option")
            return
        }

        setCreateCardLoading(true)
        setError(null)

        try {
            if (isCorporateUser) {
                const data = {
                    b2bClientId: user?.recordId,
                    clientId: onboarding.clientID,
                    userId: onboarding._id,
                    embossName1: onboarding.personalInfo.firstName + " " + onboarding.personalInfo.lastName,
                    productCode: selectedOption, // Now dynamic based on API response
                    nickname: nickName.trim() || undefined,
                }
                const response = await axiosInstance.post("b2b/createCard/virtual", data)
                console.log("Corporate card created:", response.data)
            } else {

                const data = {
                    clientId: onboarding.clientID,
                    userId: onboarding._id,
                    embossName1: onboarding.personalInfo.firstName + " " + onboarding.personalInfo.lastName,
                    currencyCode: 978,
                    accNo: account?.accountNumber,
                    productCode: selectedOption,
                    nickname: nickName.trim() || undefined,
                }
                const response = await axiosInstance.post("client/createCard/virtual", data)
                console.log("Regular card created:", response.data)
            }

            setDialogOpen(false)
            setSelectedOption("")
            setNickName("")
        } catch (err) {
            console.dir(err)
            setError("Card creation failed")
        } finally {
            alertHelper.showToast("Card successfully created!","success")
            onApiSuccess()
            setCreateCardLoading(false)
        }
    }

    const handleDialogClose = (open: boolean) => {
        if (!open) {
            setSelectedOption("")
            setNickName("")
            setError(null)
        }
        setDialogOpen(open)
    }

    return (
        <>
      <div className="flex gap-2">
        <Button onClick={handleCreateCard} disabled={!!error || createCardLoading}>
          {createCardLoading ? (
              <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Requesting...
            </>
          ) : (
              "Create Virtual Card"
          )}
        </Button>
      </div>

      <Dialog open={dialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Virtual Card</DialogTitle>
          </DialogHeader>

            {dropdownLoading ? (
                <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading available products...</span>
            </div>
            ) : (
                <div className="space-y-4">
              <div className="space-y-2">
                <Label className="font-semibold">
                  Product Type <span className="text-red-500">*</span>
                </Label>
                <Select value={selectedOption} onValueChange={setSelectedOption}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Product Type" />
                  </SelectTrigger>
                  <SelectContent>
                    {isCorporateUser ? (

                        corporateAvailableProducts.length > 0 ? (
                            corporateAvailableProducts.map((product) => (
                                <SelectItem key={product.id} value={product.code}>
                            {product.description}
                          </SelectItem>
                            ))
                        ) : (
                            <SelectItem value="" disabled>
                          No products available
                        </SelectItem>
                        )
                    ) : (
                        availableProducts.map((v) => (
                            <SelectItem key={v._id} value={v.version_code}>
                          {v.version_name} ({v.version_code})
                        </SelectItem>
                        ))
                    )}
                  </SelectContent>
                </Select>
                  {isCorporateUser && corporateAvailableProducts.length === 0 && !dropdownLoading && (
                      <p className="text-sm text-muted-foreground">
                    No corporate products available. Please contact your administrator.
                  </p>
                  )}
              </div>


            </div>
            )}

            {error && <p className="text-red-500 text-sm mt-2">{error}</p>}

            <DialogFooter>
            <Button variant="outline" onClick={() => handleDialogClose(false)} disabled={createCardLoading}>
              Cancel
            </Button>
            <Button onClick={handleDialogSubmit} disabled={createCardLoading || !selectedOption}>
              {createCardLoading ? (
                  <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                  "Create"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
    )
}
