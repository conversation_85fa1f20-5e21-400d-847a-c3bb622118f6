//@ts-nocheck
"use client"
import type React from "react"
import { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
    AlertCircle,
    CreditCard,
    MapPin,
    Truck,
    User,
    Phone,
    Mail,
    Calendar,
    Globe,
    Plus,
    Check,
    Zap,
    PlaneTakeoff,
    DollarSign,
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import axios from "axios"
import axiosInstance from "@/utils/axiosInstance"
import { useRouter } from "next/navigation"
import "intl-tel-input/styles"
import "intl-tel-input/build/css/intlTelInput.css"
import { Label } from "@/components/ui/label"
import { countries, country_currency as ListOfCountries, countryNameByCode, formatPrice } from "@/utils/data"
import { formatDob } from "@/utils/helpers"
import { alertHelper } from "@/utils/alertHelper"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import PhoneNumberDisplay from "@/components/PhoneDispaly"
import type { CardImage } from "@types/types"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

// Define types for the data
interface Address {
    street: string
    building: string
    apartmentNo?: string
    city: string
    state: string
    postalCode: string
    country: string
}

interface Client {
    id: string
    address: Address
    phoneNumber?: string
}

const asset = process.env.NEXT_PUBLIC_ASSET_URL;

type DeliveryMethodCode = string

export interface DeliveryMethod {
    _id: string
    zoneId: string | Zone
    country: string
    methodId: string | DeliveryMethodCode
    standardWeight: number
    bulkWeight: number
    deliveryTime: string
    price: number
    isActive: boolean
    source?: "regular" | "pp"
    weightRates?: { weight: number; rate: number }[]
}

interface ValidationErrors {
    productType?: string
    deliveryAddress?: string
    deliveryType?: string
    authPhoneNumber?: string
}

interface Country {
    _id: string
    country_code: string
    country_name: string
    status: string
    is_active: boolean
}

interface PhoneInputValue {
    countryCode: string
    nationalNumber: string
    isValid: boolean
    fullNumber: string
}

export interface Zone {
    _id: string
    name: string
}

// Add interface for fee response
interface FeeResponse {
    success: boolean
    fee?: number
    currency?: string
    message?: string
}

export default function CreatePhysicalCard({ params }: { params: { id: string } }) {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isAddingAddress, setIsAddingAddress] = useState(false)
    const [client, setClient] = useState<Client | null>(null)
    const [itClient, setItClient] = useState<Client | null>(null)
    const [deliveryMethods, setDeliveryMethods] = useState<DeliveryMethod[]>([])
    const [PPDeliveryMethods, setPPDeliveryMethods] = useState<[]>([])
    const [account, setAccount] = useState<null | any>(null)
    const [companyData, setCompanyData] = useState<any>(null)
    const [cip, setCip] = useState<any[]>([])
    const [savedCountries, setSavedCountries] = useState<Country[]>([])
    const [combinedDeliveryMethods, setCombinedDeliveryMethods] = useState<DeliveryMethod[]>([])
    const [allPPDeliveryMethods, setAllPPDeliveryMethods] = useState<any[]>([])

    // Add fee-related state
    const [isCheckingFee, setIsCheckingFee] = useState(false)
    const [cardIssuingFee, setCardIssuingFee] = useState<number | null>(null)
    const [feeError, setFeeError] = useState<string | null>(null)

    const [formData, setFormData] = useState({
        productType: "",
        embossName1: "",
        embossName2: "",
        nickname: "",
        deliveryAddress: "",
        deliveryType: "",
        authPhoneNumber: "",
    })

    // Validation state
    const [touched, setTouched] = useState({
        productType: false,
        deliveryAddress: false,
        deliveryType: false,
        authPhoneNumber: false,
    })

    const [errors, setErrors] = useState<ValidationErrors>({})
    const [formSubmitted, setFormSubmitted] = useState(false)
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [currentImage, setCurrentImage] = useState(null)
    const [phoneValue, setPhoneValue] = useState<PhoneInputValue>({
        countryCode: "+43",
        nationalNumber: "",
        isValid: false,
        fullNumber: "+43",
    })

    const [currentCardImage, setCurrentCardImage] = useState<CardImage | null>(null)
    const [selectedAddress, setSelectedAddress] = useState("")
    const [newAddress, setNewAddress] = useState({
        building: "",
        street: "",
        apartmentNo: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
    })
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [balance, setBalance] = useState<number | null>(null)
    const [isLoadingBalance, setIsLoadingBalance] = useState(false)
    const [balanceError, setBalanceError] = useState<string | null>(null)

    // Add function to check card issuing fee
    const checkCardIssuingFee = async (productType: string, deliveryMethod?: string) => {
        if (!client?.address?.country || !productType) return

        setIsCheckingFee(true)
        setFeeError(null)

        try {
            // Get country code from client address
            const countryData = countries.find((c) => c.isoNumeric === client.address.country)
            const locationCode = countryData?.iso2 || "BG" // Default to BG if not found

            // Determine modifier based on delivery method
            let modifier = "DHL" // Default
            if (deliveryMethod) {
                const selectedDeliveryMethod = combinedDeliveryMethods.find((m) => m._id === deliveryMethod)
                if (selectedDeliveryMethod?.source === "pp") {
                    modifier = "PP" // Polish Post
                } else if (selectedDeliveryMethod?.methodId?.toLowerCase().includes("express")) {
                    modifier = "DHL"
                } else if (selectedDeliveryMethod?.methodId?.toLowerCase().includes("air")) {
                    modifier = "DHL_AIR"
                } else if (selectedDeliveryMethod?.methodId?.toLowerCase().includes("road")) {
                    modifier = "DHL_ROAD"
                }
            }

            // Get product name from available products
            const selectedProduct = availableProducts.find((p) => p.version_code === productType)
             // Determine modifier based on delivery method
            let customerType = "Consumer" // Default


                if (selectedProduct.version_name.toLowerCase().includes("consumer")) {
                    customerType = "Consumer" // Polish Post
                } else if (selectedProduct.version_name.toLowerCase().includes("business")) {
                    customerType = "Business"
                }

            const productTypeName =   "Debit Physical Card"

            const requestData = {
                OperationType: "Card Issuing Fee",
                CustomerType: customerType,
                ProductType: productTypeName,
                Location: locationCode,
                Modifier: modifier,
            }

            const response = await axiosInstance.post("fee/checkFee", requestData)

            if (response.data) {
                setCardIssuingFee(response.data.fixedFee || 0)
            } else {
                setFeeError(response.data.message || "Failed to fetch card issuing fee")
                setCardIssuingFee(null)
            }
        } catch (error) {
            console.error("Error checking card issuing fee:", error)
            setFeeError("Failed to check card issuing fee")
            setCardIssuingFee(null)
        } finally {
            setIsCheckingFee(false)
        }
    }

    // Function to combine and process delivery methods
    const processCombinedDeliveryMethods = (dhlMethods, ppMethods) => {
        // Process DHL delivery methods
        const dhlFormattedMethods = dhlMethods.map((method) => ({
            _id: `dhl-${method._id}`,
            zoneId: method.DHL_ZONE_NAME,
            zoneName: method.DHL_ZONE_NAME,
            country: method.Country,
            methodId: "DHL Express",
            standardWeight: 50,
            bulkWeight: 0,
            deliveryTime: method["DHL_EXPRESS  DELIVERY TIMES"],
            price: method["DHL_EXPRESS 100g"],
            isActive: true,
            source: "regular" as const,
            weightRates: [
                { weight: 50, rate: method["DHL_EXPRESS 50g"] },
                { weight: 100, rate: method["DHL_EXPRESS 100g"] },
            ],
        }))

        // Add DHL Air if available
        if (dhlMethods.length > 0 && dhlMethods[0]["DHL_Air 100g"]) {
            dhlFormattedMethods.push({
                _id: `dhl-air-${dhlMethods[0]._id}`,
                zoneId: dhlMethods[0].DHL_ZONE_NAME,
                zoneName: dhlMethods[0].DHL_ZONE_NAME,
                country: dhlMethods[0].Country,
                methodId: "DHL Air",
                standardWeight: 50,
                bulkWeight: 0,
                deliveryTime: dhlMethods[0]["DHL_AIR  DELIVERY TIMES"],
                price: dhlMethods[0]["DHL_Air 100g"],
                isActive: true,
                source: "regular" as const,
                weightRates: [
                    { weight: 50, rate: dhlMethods[0]["DHL_Air 50g"] },
                    { weight: 100, rate: dhlMethods[0]["DHL_Air 100g"] },
                ],
            })
        }

        // Add DHL Road if available
        if (dhlMethods.length > 0 && dhlMethods[0]["DHL_ROAD_100g"]) {
            dhlFormattedMethods.push({
                _id: `dhl-road-${dhlMethods[0]._id}`,
                zoneId: dhlMethods[0].DHL_ZONE_NAME,
                zoneName: dhlMethods[0].DHL_ZONE_NAME,
                country: dhlMethods[0].Country,
                methodId: "DHL Road",
                standardWeight: 50,
                bulkWeight: 0,
                deliveryTime: dhlMethods[0]["DHL_ROAD  DELIVERY TIMES"],
                price: dhlMethods[0]["DHL_ROAD_100g"],
                isActive: true,
                source: "regular" as const,
                weightRates: [
                    { weight: 50, rate: dhlMethods[0]["DHL_ROAD_50g"] },
                    { weight: 100, rate: dhlMethods[0]["DHL_ROAD_100g"] },
                ],
            })
        }

        // Process Polish Post delivery methods
        const ppMethodsFormatted = ppMethods.map((method) => ({
            _id: `pp-${method._id}`,
            zoneId: method.Poczta_Post_Category,
            zoneName: method.Poczta_Post_Category,
            country: method.Country,
            methodId: "Polish Post",
            standardWeight: 50,
            bulkWeight: 0,
            deliveryTime: method.PPDeliveryTime,
            price: method["Poczta_Post 100g"],
            isActive: true,
            source: "pp" as const,
            weightRates: [
                { weight: 50, rate: method["Poczta_Post 50g"] },
                { weight: 100, rate: method["Poczta_Post 100g"] },
                { weight: 350, rate: method["Poczta_Post 350g"] },
                { weight: 500, rate: method["Poczta_Post 500g"] },
            ],
        }))

        // Combine both types of delivery methods
        const combined = [...ppMethodsFormatted, ...dhlFormattedMethods]
        setCombinedDeliveryMethods(combined)
    }

    // Get the current country from the client's address
    const currentCountry = client?.address?.country
        ? countries.find((c) => c.isoNumeric === client.address.country)?.name
        : null

    // Filter delivery methods by the current country
    const filteredDeliveryMethods = combinedDeliveryMethods

    // Get available physical card products
    const availableProducts = cip.flatMap((program) =>
        program.productVersionName.filter((r) => r.status === "active" && r.version_name.toLowerCase().includes("phy")),
    )

    const isBalanceSufficient = () => {
        if (balance === null || !formData.deliveryType) return false
        const selectedMethod = combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)
        const totalCost = (selectedMethod?.price || 0) + (cardIssuingFee || 0)
        return balance >= totalCost
    }

    const getSelectedDeliveryPrice = () => {
        if (!formData.deliveryType) return 0
        const selectedMethod = combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)
        return selectedMethod?.price || 0
    }

    const getTotalOrderCost = () => {
        return getSelectedDeliveryPrice() + (cardIssuingFee || 0)
    }

    // Validate form fields
    const validateForm = useCallback(() => {
        const newErrors: ValidationErrors = {}
        if (!formData.productType) {
            newErrors.productType = "Product type is required"
        }
        if (!formData.deliveryType) {
            newErrors.deliveryType = "Delivery type is required"
        } else {
            // Enhanced balance validation including card issuing fee
            const selectedMethod = combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)
            if (selectedMethod && balance !== null) {
                const totalCost = selectedMethod.price + (cardIssuingFee || 0)
                if (balance < totalCost) {
                    newErrors.deliveryType = `Insufficient balance. Required: ${formatPrice(totalCost)}, Available: ${formatPrice(balance)}`
                }
            }
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }, [formData, balance, combinedDeliveryMethods, cardIssuingFee])

    // Mark field as touched when user interacts with it
    const handleBlur = (field: keyof typeof touched) => {
        setTouched((prev) => ({ ...prev, [field]: true }))
    }

    // Fetch countries data
    useEffect(() => {
        const fetchCountries = async () => {
            setIsLoading(true)
            try {
                const response = await axiosInstance.get("companies/country")
                setSavedCountries(response.data)
            } catch (error) {
                console.error("Error fetching countries", error)
                alertHelper.showToast("Failed to load countries data", "error")
            } finally {
                setIsLoading(false)
            }
        }
        fetchCountries()
    }, [])

    // Auto-select product type if only one available
    useEffect(() => {
        if (availableProducts.length === 1 && !formData.productType) {
            setFormData((prev) => ({
                ...prev,
                productType: availableProducts[0].version_code,
            }))
            // Clear any errors for this field
            setErrors((prev) => ({ ...prev, productType: undefined }))
            // Check fee for auto-selected product
            checkCardIssuingFee(availableProducts[0].version_code)
        }
    }, [availableProducts, formData.productType])

    // Auto-select delivery method if only one option available
    useEffect(() => {
        if (
            filteredDeliveryMethods.length === 1 &&
            (!formData.deliveryType || formData.deliveryType !== filteredDeliveryMethods[0]._id)
        ) {
            setFormData((prev) => ({ ...prev, deliveryType: filteredDeliveryMethods[0]._id }))
            // Clear any errors for this field
            setErrors((prev) => ({ ...prev, deliveryType: undefined }))
            // Re-check fee with delivery method
            if (formData.productType) {
                checkCardIssuingFee(formData.productType, filteredDeliveryMethods[0]._id)
            }
        }
    }, [filteredDeliveryMethods, formData.deliveryType, formData.productType])

    // Validate form when values change
    useEffect(() => {
        if (formSubmitted) {
            validateForm()
        }
    }, [formData, selectedAddress, phoneValue, formSubmitted, validateForm])

    // Fetch initial data
    useEffect(() => {
        fetchData()
    }, [params.id])

    // Fetch delivery methods when client address country changes
    useEffect(() => {
        if (client?.address?.country) {
            const countryName = countries.find((c) => c.isoNumeric === client.address.country)?.name
            if (countryName) {
                fetchCountryDeliveryMethods(countryName)
            }
        }
    }, [client?.address?.country])

    const fetchCountryDeliveryMethods = async (countryName) => {
        try {
            setIsLoading(true)
            const response = await fetch(`/api/get-country-delivery-methods?country=${countryName}`)
            const data = await response.json()
            if (data.success) {
                // Process Polish Post delivery methods
                const ppMethods = data.polishPost?.records || []
                setPPDeliveryMethods(ppMethods)
                setAllPPDeliveryMethods(ppMethods)
                // Process DHL delivery methods
                const dhlMethods = data.dhl?.records || []
                setDeliveryMethods(dhlMethods)
                // Process and combine all delivery methods
                processCombinedDeliveryMethods(dhlMethods, ppMethods)
            } else {
                alertHelper.showToast(`Failed to load delivery methods for ${countryName}`, "error")
            }
        } catch (error) {
            console.error("Error fetching country delivery methods:", error)
            alertHelper.showToast("Failed to load delivery methods", "error")
        } finally {
            setIsLoading(false)
        }
    }

    const fetchData = async () => {
        setIsLoading(true)
        try {
            // Use Promise.all to fetch data in parallel
            const [clientResponse] = await Promise.all([axiosInstance.get(`/onboarding/personal/${params.id}`)])

            // Extract client data from response
            const clientData = clientResponse.data.data
            setClient(clientData)
            setAccount(clientResponse.data.account[0])
            setBalance(clientResponse.data.balance)

            // Get country from client address
            const countryName = clientData?.address?.country
                ? countries.find((c) => c.isoNumeric === clientData.address.country)?.name
                : "ALBANIA" // Default fallback

            // Fetch delivery methods for the specific country
            const countryDeliveryResponse = await fetch(`/api/get-country-delivery-methods?country=${countryName}`)
            const countryDeliveryData = await countryDeliveryResponse.json()

            if (countryDeliveryData.success) {
                // Process Polish Post delivery methods
                const ppMethods = countryDeliveryData.polishPost?.records || []
                setPPDeliveryMethods(ppMethods)
                setAllPPDeliveryMethods(ppMethods)
                // Process DHL delivery methods
                const dhlMethods = countryDeliveryData.dhl?.records || []
                setDeliveryMethods(dhlMethods)
                // Process and combine all delivery methods
                processCombinedDeliveryMethods(dhlMethods, ppMethods)
            } else {
                alertHelper.showToast("Failed to load delivery methods for this country", "error")
            }

            // If company exists, fetch company data
            if (clientData?.company?._id) {
                fetchCompanyData(clientData.company._id)
            }

            // Set phone data if available
            if (clientData.authPhoneNumber) {
                const phoneNumber = clientData.authPhoneNumber
                setPhoneValue({
                    fullNumber: phoneNumber,
                })
                setFormData((prev) => ({
                    ...prev,
                    authPhoneNumber: phoneNumber,
                }))
            }

            // Set default selected address if client has an address
            if (clientData.address) {
                setSelectedAddress("default")
                setFormData((prev) => ({
                    ...prev,
                    deliveryAddress: "default",
                }))
            }
        } catch (error) {
            console.error("Error fetching data:", error)
            alertHelper.showToast("Failed to load required data", "error")
        } finally {
            setIsLoading(false)
        }
    }

    const fetchCompanyData = async (companyId) => {
        setIsLoading(true)
        try {
            const response = await axiosInstance.get(`/company/${companyId}`)
            setCompanyData(response.data.company)
            setCip(response.data.cip || [])
        } catch (err) {
            console.error("Failed to fetch company data", err)
            alertHelper.showToast("Failed to load company data", "error")
        } finally {
            setIsLoading(false)
        }
    }

    // Add this constant at the top of the component (copy from IndividualOnboardingPage):
    const NEXT_PUBLIC_DEFAULT_COMPANY_ID = process.env.NEXT_PUBLIC_DEFAULT_COMPANY_ID;
    
    // Add this new function to process card images (copy from IndividualOnboardingPage logic):
    const processCardImage = useCallback((images: CardImage[], productCode: string, companyId: string) => {
        try {
            const matchingImages = images.filter((img) => img.product_version.version_code === productCode)
            let companyImage = matchingImages.find((img) => img.company._id === companyId)
            // Fallback to default company if no match found
            if (!companyImage) {
                companyImage = matchingImages.find((img) => img.company._id === NEXT_PUBLIC_DEFAULT_COMPANY_ID)
            }
            if (companyImage) {
                setCurrentCardImage(companyImage)
            } else {
                setCurrentCardImage(null)
            }
        } catch (error) {
            console.error("Error processing card image:", error)
            setCurrentCardImage(null)
        }
    }, [])

    // Replace the existing fetchImagesData function with this improved version:
    const fetchImagesData = async () => {
        try {
            setIsLoading(true)
            const response = await axiosInstance.get<CardImage[]>("/images")
            setCardImages(response.data.data)
            // Process card images when we have both images and form data
            if (response.data.data && formData.productType) {
                processCardImage(response.data.data, formData.productType, companyData?._id || client?.company?._id)
            }
        } catch (error) {
            console.error("Error fetching data", error)
        } finally {
            setIsLoading(false)
        }
    }

    useEffect(() => {
        fetchImagesData()
    }, [params.id])

    // Add this useEffect after the existing useEffect hooks:
    useEffect(() => {
        if (cardImages.length > 0 && formData.productType) {
            processCardImage(cardImages, formData.productType, companyData?._id || client?.company?._id)
        }
    }, [cardImages, formData.productType, companyData?._id, client?.company?._id, processCardImage])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData({ ...formData, [e.target.name]: e.target.value })
    }

    const handleNewAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setNewAddress({ ...newAddress, [e.target.name]: e.target.value })
    }

    const handleCountryChange = (value) => {
        const country = ListOfCountries.find((c) => c.iso3.toLowerCase() === value.toLowerCase())?.numericCode
        setNewAddress({ ...newAddress, country })
    }

    const handleAddNewAddress = async () => {
        // Validate required fields
        if (
            !newAddress.building ||
            !newAddress.street ||
            !newAddress.city ||
            !newAddress.state ||
            !newAddress.postalCode ||
            !newAddress.country
        ) {
            alertHelper.showToast("Please fill in all required address fields", "error")
            return
        }

        setIsAddingAddress(true)
        const data = {
            clientId: client.clientID,
            building: newAddress.building,
            street: newAddress.street,
            apartment: newAddress.apartmentNo || "", // optional
            city: newAddress.city,
            stateProvince: newAddress.state,
            postalCode: newAddress.postalCode,
            country: newAddress.country,
        }

        try {
            await axiosInstance.post(`client/addAddress`, data)
            alertHelper.showToast("Address added successfully", "success")
            // Refresh data to get the new address
            await fetchData()
            // Set selected address
            setSelectedAddress("new")
            const fullAddress = `${newAddress.building}, ${newAddress.street}, ${newAddress.apartmentNo || ""}, ${newAddress.city}, ${newAddress.state}, ${newAddress.postalCode}, ${newAddress.country}`
            setFormData((prev) => ({
                ...prev,
                deliveryAddress: fullAddress,
            }))
            // Close dialog
            setIsDialogOpen(false)
            // Clear any delivery address error
            setErrors((prev) => ({ ...prev, deliveryAddress: undefined }))
        } catch (error) {
            console.error("Failed to add address:", error)
            alertHelper.showToast("An error occurred. Please try again.", "error")
        } finally {
            setIsAddingAddress(false)
        }
    }

    const [showConfirmation, setShowConfirmation] = useState(false)

    const handleSubmitStep1 = async (e: React.FormEvent) => {
        e.preventDefault()
        setFormSubmitted(true)
        // Mark all fields as touched
        setTouched({
            productType: true,
            deliveryAddress: true,
            deliveryType: true,
            authPhoneNumber: true,
        })
        // Validate all fields
        const isValid = validateForm()
        if (!isValid) {
            // Scroll to the first error
            const firstError = document.querySelector(".error-message")
            if (firstError) {
                firstError.scrollIntoView({ behavior: "smooth", block: "center" })
            }
            return
        }
        // Show confirmation dialog instead of submitting
        setShowConfirmation(true)
    }

    const handleConfirmOrder = async () => {
        setIsSubmitting(true)
        try {
            const data = {
                clientId: client.clientID,
                userId: client._id,
                productCode: formData.productType,
                embossName1: formData.embossName1 || `${client.personalInfo.firstName} ${client.personalInfo.lastName}`,
                embossName2: formData.embossName2 || "",
                nickname: formData.nickname || "",
                currencyCode: 978,
                accNo: account.accountNumber,
                phoneNumber: client.personalInfo.authPhoneNumber,
            }

            // Determine if the selected delivery method is from PP
            const selectedMethod = combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)
            const isPPMethod = selectedMethod?.source === "pp"

            // Add delivery method info to the data object
            data.deliveryMethod = {
                id: formData.deliveryType,
                source: isPPMethod ? "latter" : "courier",
                methodId: selectedMethod?.methodId,
                price: Number.parseFloat(selectedMethod?.price.toFixed(2)),
            }
            data.country =  ListOfCountries.find((c) => c.numericCode === client.address.country)?.countryNameCode;

            await axiosInstance.post(`client/createCard/physical`, data)
            alertHelper.showToast("Congratulations, your card is now created.", "success")
            router.back()
        } catch (error) {
            console.error("Error submitting form:", error)
            alertHelper.showToast("An error occurred. Please try again.", "error")
        } finally {
            setIsSubmitting(false)
            setShowConfirmation(false)
        }
    }

    // Update the getCardImageUrl function:
    const getCardImageUrl = () => {
        if (currentCardImage) {
            return `${asset}${currentCardImage.front_side}`
        }
        return "/placeholder.svg?height=380&width=240"
    }

    // Handle delivery method change
    const handleDeliveryMethodChange = (value: string) => {
        console.log("Delivery method changed to:", value)
        setFormData((prev) => ({ ...prev, deliveryType: value }))

        // Re-check fee with new delivery method
        if (formData.productType) {
            checkCardIssuingFee(formData.productType, value)
        }

        // Immediately check balance when delivery method changes
        const selectedMethod = combinedDeliveryMethods.find((m) => m._id === value)
        if (selectedMethod && balance !== null) {
            const totalCost = selectedMethod.price + (cardIssuingFee || 0)
            if (balance < totalCost) {
                setErrors((prev) => ({
                    ...prev,
                    deliveryType: `Insufficient balance. Required: ${formatPrice(totalCost)}, Available: ${formatPrice(balance)}`,
                }))
            } else {
                setErrors((prev) => ({ ...prev, deliveryType: undefined }))
            }
        } else {
            setErrors((prev) => ({ ...prev, deliveryType: undefined }))
        }
        handleBlur("deliveryType")
    }

    // Helper function to get delivery method styling
    const getDeliveryMethodStyling = (method: DeliveryMethod, isSelected: boolean) => {
        const isDHLExpress = method.methodId === "DHL Express"
        if (isDHLExpress) {
            return {
                containerClass: isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50",
                iconClass: "text-orange-600",
                priceClass: "text-orange-600 font-bold",
                badgeClass: "bg-orange-100 text-orange-800 border-orange-300",
            }
        }
        return {
            containerClass: isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50",
            iconClass: "text-muted-foreground",
            priceClass: "text-primary",
            badgeClass: "bg-secondary text-secondary-foreground",
        }
    }

    // Loading state
    if (isLoading) {
        return <LoadingOverlay />
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-background to-muted/30 p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
          <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">Create Physical Card</h1>
          <p className="text-muted-foreground text-lg">Order your new physical card with secure delivery</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Client Information Sidebar */}
            <div className="lg:col-span-1">
            <Card className="sticky top-6 shadow-lg border-0 bg-card/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-foreground">
                  <User className="h-5 w-5 text-primary" />
                  Client Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {client && (
                    <>
                    {/* Personal Details */}
                        <div className="space-y-3">
                      <h3 className="font-semibold text-foreground border-b pb-2">Personal Details</h3>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-start gap-3">
                          <User className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">
                              {client.personalInfo.firstName} {client.personalInfo.middleName}{" "}
                                {client.personalInfo.lastName}
                            </p>
                            <p className="text-muted-foreground">Full Name</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <Calendar className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{formatDob(client.personalInfo.dateOfBirth)}</p>
                            <p className="text-muted-foreground">Date of Birth</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <Globe className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{countryNameByCode(client.citizenship)}</p>
                            <p className="text-muted-foreground">Nationality</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <Mail className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{client.personalInfo.email}</p>
                            <p className="text-muted-foreground">Email</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3 hidden">
                          <Phone className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">
                              <PhoneNumberDisplay phoneNumber={client.personalInfo.phone} />
                            </p>
                            <p className="text-muted-foreground">Phone Number</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <Phone className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-primary">
                              <PhoneNumberDisplay phoneNumber={client.personalInfo.authPhoneNumber} />
                            </p>
                            <p className="text-muted-foreground">3DS Authorised Mobile</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <CreditCard className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">
                              {client.embossName1 || `${client.personalInfo.firstName} ${client.personalInfo.lastName}`}
                            </p>
                            <p className="text-muted-foreground">Emboss Name 1</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <CreditCard className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{client.embossName2 || "N/A"}</p>
                            <p className="text-muted-foreground">Emboss Name 2</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                        {/* Account Balance */}
                        <div className="space-y-3">
                      <h3 className="font-semibold text-foreground border-b pb-2">Account Balance</h3>
                      <div className="flex items-start gap-3 text-sm">
                        <CreditCard className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div>
                          {isLoadingBalance ? (
                              <div className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                              <span className="text-muted-foreground">Loading...</span>
                            </div>
                          ) : balanceError ? (
                              <div className="flex items-center gap-2">
                              <AlertCircle className="h-4 w-4 text-destructive" />
                              <span className="text-destructive">{balanceError}</span>
                            </div>
                          ) : (
                              <>
                              <p className="font-medium text-foreground text-lg">
                                {balance !== null ? formatPrice(balance) : "N/A"}
                              </p>
                              <p className="text-muted-foreground">Available Balance</p>
                                  {formData.deliveryType && (
                                      <div className="mt-2 p-2 rounded-md bg-muted/50 space-y-1">
                                  <p className="text-xs text-muted-foreground">
                                    Delivery Cost: {formatPrice(getSelectedDeliveryPrice())}
                                  </p>
                                          {cardIssuingFee !== null && (
                                              <p className="text-xs text-muted-foreground">
                                      Card Issuing Fee: {formatPrice(cardIssuingFee)}
                                    </p>
                                          )}
                                          <p className="text-xs font-medium border-t pt-1">
                                    Total Cost: {formatPrice(getTotalOrderCost())}
                                  </p>
                                  <p
                                      className={`text-xs font-medium ${isBalanceSufficient() ? "text-green-600" : "text-destructive"}`}
                                  >
                                    {isBalanceSufficient() ? "✓ Sufficient funds" : "✗ Insufficient funds"}
                                  </p>
                                </div>
                                  )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <Separator />

                        {/* Address */}
                        <div className="space-y-3">
                      <h3 className="font-semibold text-foreground border-b pb-2">Current Address</h3>
                      <div className="flex items-start gap-3 text-sm">
                        <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div>
                          <address className="not-italic font-medium text-foreground">
                            {client.address.buildingNumber} {client.address.street}
                              {client.address.apartment ? `, ${client.address.apartment}` : ""}
                              <br />
                              {client.address.city}, {client.address.stateProvince} {client.address.zipCode}
                              <br />
                              {ListOfCountries.find((c) => c.numericCode === client.address.country)?.countryName || ""}
                          </address>
                          <p className="text-muted-foreground">Registered Address</p>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>

            {/* Main Form */}
            <div className="lg:col-span-2">
            <Card className="shadow-lg border-0 bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-foreground">
                  <CreditCard className="h-5 w-5 text-primary" />
                  Card Configuration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmitStep1} className="space-y-8">
                  {/* Form validation summary */}
                    {formSubmitted && Object.keys(errors).length > 0 && (
                        <Alert variant="destructive" className="border-destructive/50 bg-destructive/10">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>Please correct the errors below before continuing.</AlertDescription>
                    </Alert>
                    )}

                    {/* Balance warning */}
                    {balance !== null && formData.deliveryType && !isBalanceSufficient() && (
                        <Alert variant="destructive" className="border-destructive/50 bg-destructive/10">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Insufficient balance for selected delivery method. Required: {formatPrice(getTotalOrderCost())},
                        Available: {formatPrice(balance)}
                      </AlertDescription>
                    </Alert>
                    )}

                    {/* Product Type */}
                    <div className="space-y-3">
                    <Label className="text-base font-semibold text-foreground">
                      Product Type <span className="text-destructive">*</span>
                    </Label>
                    <Select
                        value={formData.productType}
                        onValueChange={(value) => {
                            setFormData({ ...formData, productType: value })
                            setErrors((prev) => ({ ...prev, productType: undefined }))
                            // Process card image when product type changes
                            if (cardImages.length > 0) {
                                processCardImage(cardImages, value, companyData?._id || client?.company?._id)
                            }
                            // Check fee when product type changes
                            checkCardIssuingFee(value, formData.deliveryType)
                        }}
                        onOpenChange={() => handleBlur("productType")}
                        disabled={availableProducts.length === 1}
                    >
                      <SelectTrigger
                          className={`h-12 ${touched.productType && errors.productType ? "border-destructive" : "border-input"}`}
                      >
                        <SelectValue placeholder="Select Product Type" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableProducts.length > 0 ? (
                            availableProducts.map((v) => (
                                <SelectItem key={v._id} value={v.version_code}>
                              <div className="flex items-center gap-2">
                                <CreditCard className="h-4 w-4" />
                                  {v.version_name} ({v.version_code})
                              </div>
                            </SelectItem>
                            ))
                        ) : (
                            <SelectItem disabled>No product available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                        {touched.productType && errors.productType && (
                            <p className="text-destructive text-sm flex items-center gap-1 error-message">
                        <AlertCircle className="h-3 w-3" />
                                {errors.productType}
                      </p>
                        )}

                      {/*  /!* Card Issuing Fee Display *!/*/}
                      {/*  {formData.productType && (*/}
                      {/*      <div className="mt-3 p-3 bg-muted/30 border border-border rounded-lg">*/}
                      {/*  <div className="flex items-center gap-2 mb-2">*/}
                      {/*    <DollarSign className="h-4 w-4 text-primary" />*/}
                      {/*    <span className="font-medium text-sm">Card Issuing Fee</span>*/}
                      {/*  </div>*/}
                      {/*          {isCheckingFee ? (*/}
                      {/*              <div className="flex items-center gap-2">*/}
                      {/*      <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />*/}
                      {/*      <span className="text-sm text-muted-foreground">Checking fee...</span>*/}
                      {/*    </div>*/}
                      {/*          ) : feeError ? (*/}
                      {/*              <div className="flex items-center gap-2">*/}
                      {/*      <AlertCircle className="h-4 w-4 text-destructive" />*/}
                      {/*      <span className="text-sm text-destructive">{feeError}</span>*/}
                      {/*    </div>*/}
                      {/*          ) : cardIssuingFee !== null ? (*/}
                      {/*              <p className="text-sm font-medium text-primary">{formatPrice(cardIssuingFee)}</p>*/}
                      {/*          ) : (*/}
                      {/*              <p className="text-sm text-muted-foreground">Fee information not available</p>*/}
                      {/*          )}*/}
                      {/*</div>*/}
                      {/*  )}*/}
                  </div>

                    {/* Nickname */}
                    <div className="space-y-3 hidden">
                    <Label className="text-base font-semibold text-foreground">Card Nickname</Label>
                    <Input
                        name="nickname"
                        placeholder="Enter a nickname for this card (optional)"
                        value={formData.nickname}
                        onChange={handleChange}
                        className="h-12 border-input"
                    />
                    <p className="text-xs text-muted-foreground">A personal name to help you identify this card</p>
                  </div>

                    {/* Delivery Address */}
                    <div className="space-y-4">
                    <Label className="text-base font-semibold text-foreground">
                      Delivery Address <span className="text-destructive">*</span>
                    </Label>
                    <RadioGroup
                        value={selectedAddress}
                        onValueChange={(value) => {
                            setSelectedAddress(value)
                            if (value === "default" && client?.address) {
                                const address = client.address
                                const countryName =
                                    ListOfCountries.find((c) => c.numericCode === address.country)?.countryName || ""
                                setFormData((prev) => ({
                                    ...prev,
                                    deliveryAddress: `${address.street}, ${address.buildingNumber}, ${address.city}, ${address.state}, ${address.postalCode}, ${countryName}`,
                                }))
                            }
                            setErrors((prev) => ({ ...prev, deliveryAddress: undefined }))
                            handleBlur("deliveryAddress")
                        }}
                        className="space-y-3"
                    >
                      {/* Default Address */}
                        {client?.address && (
                            <div
                                className={`flex items-start space-x-3 p-4 border-2 rounded-xl transition-all cursor-pointer ${selectedAddress === "default" ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"}`}
                            >
                          <RadioGroupItem value="default" id="default-address" className="mt-1" />
                          <div className="flex-1">
                            <Label htmlFor="default-address" className="cursor-pointer">
                              <div className="flex items-center gap-2 mb-1">
                                <MapPin className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">Current Address</span>
                                <Badge variant="secondary" className="text-xs">
                                  Default
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {client.address.buildingNumber} {client.address.street} {client.address.apartment},{" "}
                                  {client.address.city}, {client.address.stateProvince}, {client.address.zipCode}
                                  &nbsp;{" "}
                                  {ListOfCountries.find((c) => c.numericCode === client.address.country)?.countryName}
                              </p>
                            </Label>
                          </div>
                        </div>
                        )}

                        {/* Add New Address */}
                        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                          <div className="flex items-center space-x-3 p-4 border-2 border-dashed border-border rounded-xl cursor-pointer hover:border-primary hover:bg-primary/5 transition-all">
                            <Plus className="h-5 w-5 text-muted-foreground" />
                            <span className="font-medium text-foreground">Add Different Delivery Address</span>
                          </div>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-md">
                          <DialogHeader>
                            <DialogTitle>Add New Delivery Address</DialogTitle>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="building">Building *</Label>
                                <Input
                                    id="building"
                                    name="building"
                                    placeholder="Building number"
                                    onChange={handleNewAddressChange}
                                />
                              </div>
                              <div>
                                <Label htmlFor="street">Street *</Label>
                                <Input
                                    id="street"
                                    name="street"
                                    placeholder="Street name"
                                    onChange={handleNewAddressChange}
                                />
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="apartmentNo">Apartment No</Label>
                              <Input
                                  id="apartmentNo"
                                  name="apartmentNo"
                                  placeholder="Apartment number (optional)"
                                  onChange={handleNewAddressChange}
                              />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="city">City *</Label>
                                <Input id="city" name="city" placeholder="City" onChange={handleNewAddressChange} />
                              </div>
                              <div>
                                <Label htmlFor="state">State *</Label>
                                <Input id="state" name="state" placeholder="State" onChange={handleNewAddressChange} />
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="postalCode">Postal Code *</Label>
                              <Input
                                  id="postalCode"
                                  name="postalCode"
                                  placeholder="Postal code"
                                  onChange={handleNewAddressChange}
                              />
                            </div>
                            <div>
                              <Label htmlFor="country">Country *</Label>
                              <Select onValueChange={(v) => handleCountryChange(v)}>
                                <SelectTrigger id="country">
                                  <SelectValue placeholder="Select Country" />
                                </SelectTrigger>
                                <SelectContent>
                                  {savedCountries
                                      .filter((c) => c.status === "active" && c.is_active)
                                      .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                      .map((country) => (
                                          <SelectItem key={`x-${country._id}`} value={country.country_code}>
                                        {country.country_name} ({country.country_code})
                                      </SelectItem>
                                      ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                              Cancel
                            </Button>
                            <Button onClick={handleAddNewAddress} disabled={isAddingAddress}>
                              {isAddingAddress ? "Saving..." : "Save Address"}
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </RadioGroup>
                        {touched.deliveryAddress && errors.deliveryAddress && (
                            <p className="text-destructive text-sm flex items-center gap-1 error-message">
                        <AlertCircle className="h-3 w-3" />
                                {errors.deliveryAddress}
                      </p>
                        )}
                  </div>

                    {/* Delivery Method */}
                    <div className="space-y-4">
                    <Label className="text-base font-semibold text-foreground">
                      Delivery Method <span className="text-destructive">*</span>
                    </Label>
                    <div className="max-h-[400px] overflow-y-auto">
                      <RadioGroup
                          value={formData.deliveryType}
                          onValueChange={handleDeliveryMethodChange}
                          className="space-y-3"
                      >
                        {combinedDeliveryMethods.length > 0 ? (
                            combinedDeliveryMethods.map((method) => {
                                const isSelected = formData.deliveryType === method._id
                                const styling = getDeliveryMethodStyling(method, isSelected)
                                const isDHLExpress = method.methodId.toLowerCase().includes("express")
                                const isDHLAir = method.methodId.toLowerCase().includes("air")
                                const totalCost = method.price + (cardIssuingFee || 0)
                                const hasInsufficientBalance = balance !== null && balance < totalCost
                                const isDisabled = hasInsufficientBalance

                                return (
                                    <Label
                                        key={method._id}
                                        htmlFor={`delivery-${method._id}`}
                                        className={`flex items-start space-x-3 p-4 border-2 rounded-xl transition-all cursor-pointer ${
                                            isDisabled
                                                ? "border-primary/30 opacity-60 cursor-not-allowed"
                                                : styling.containerClass
                                        }`}
                                    >
                                <RadioGroupItem
                                    value={method._id}
                                    id={`delivery-${method._id}`}
                                    className="mt-1"
                                    disabled={isDisabled}
                                />
                                <div className="flex-1">
                                  <div className="flex justify-between items-start mb-2">
                                    <div className="flex items-center gap-2">
                                      {isDHLExpress ? (
                                          <Zap
                                              className={`h-4 w-4 ${isDisabled ? "text-muted-foreground" : styling.iconClass}`}
                                          />
                                      ) : isDHLAir ? (
                                          <PlaneTakeoff
                                              className={`h-4 w-4 ${isDisabled ? "text-muted-foreground" : styling.iconClass}`}
                                          />
                                      ) : (
                                          <Truck
                                              className={`h-4 w-4 ${isDisabled ? "text-muted-foreground" : styling.iconClass}`}
                                          />
                                      )}
                                        <span
                                            className={`font-semibold cursor-pointer ${isDisabled ? "text-muted-foreground" : ""}`}
                                        >
                                        {method.methodId}
                                      </span>
                                        {isDHLExpress && !isDisabled && (
                                            <Badge className={styling.badgeClass}>
                                          <Zap className="h-3 w-3 mr-1" />
                                          Express
                                        </Badge>
                                        )}
                                        {method.source === "pp" && (
                                            <Badge variant="outline" className="text-xs">
                                          Standard Polish Post
                                        </Badge>
                                        )}
                                        {hasInsufficientBalance && (
                                            <Badge variant="destructive" className="text-xs">
                                          <AlertCircle className="h-3 w-3 mr-1" />
                                          Insufficient Balance
                                        </Badge>
                                        )}
                                    </div>
                                    <span
                                        className={`text-lg font-bold ${
                                            isDisabled ? "text-muted-foreground" : styling.priceClass
                                        }`}
                                    >
                                      {formatPrice(method.price)}
                                    </span>
                                  </div>
                                  <p
                                      className={`text-sm mb-2 ${isDisabled ? "text-muted-foreground" : "text-muted-foreground"}`}
                                  >
                                    Estimated delivery: {method.deliveryTime}
                                  </p>
                                    {hasInsufficientBalance && (
                                        <p className="text-xs text-destructive flex items-center gap-1">
                                      <AlertCircle className="h-3 w-3" />
                                      Need {formatPrice(totalCost - balance)} more to select this option
                                    </p>
                                    )}
                                </div>
                              </Label>
                                )
                            })
                        ) : (
                            <div className="text-muted-foreground p-4 border-2 border-dashed border-border rounded-xl text-center">
                            No delivery methods available for this location
                          </div>
                        )}
                      </RadioGroup>
                    </div>
                        {touched.deliveryType && errors.deliveryType && (
                            <p className="text-destructive text-sm flex items-center gap-1 error-message">
                        <AlertCircle className="h-3 w-3" />
                                {errors.deliveryType}
                      </p>
                        )}
                  </div>

                    {/* Auth Phone Number */}
                    <div className="hidden space-y-3">
                    <Label className="text-base font-semibold text-foreground">
                      Authentication Phone Number <span className="text-destructive">*</span>
                    </Label>
                    <div className="p-4 bg-muted/30 border border-border rounded-xl">
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">
                          <PhoneNumberDisplay phoneNumber={client?.personalInfo.authPhoneNumber} />
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          Verified
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Used for 3DS OTP verification</p>
                    </div>
                  </div>

                    {/* Submit Button */}
                    <div className="pt-6">
                    <Button
                        type="submit"
                        className="w-full h-12 text-lg font-semibold bg-primary hover:bg-primary/90 transition-all duration-200"
                        disabled={isSubmitting || (balance !== null && formData.deliveryType && !isBalanceSufficient())}
                    >
                      {isSubmitting
                          ? "Processing..."
                          : balance !== null && formData.deliveryType && !isBalanceSufficient()
                              ? `Insufficient Balance - Need ${formatPrice(getTotalOrderCost() - balance)} More`
                              : "Review Order"}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>

          {/* Confirmation Dialog */}
          <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
          <DialogContent className="sm:max-w-5xl p-0 overflow-hidden rounded-xl">
            <div className="grid md:grid-cols-2 h-full">
              {/* Card Image Section - Left Side */}
                <div className="bg-gradient-to-br from-primary/90 to-primary p-8 flex items-center justify-center">
                <div className="w-full flex flex-col items-center">
                  <div className="aspect-[0.63/1] w-full max-w-[240px] mx-auto rounded-xl overflow-hidden shadow-xl transform rotate-[-5deg] hover:rotate-0 transition-all duration-300 border-4 border-white/30">
                    <img
                        src={getCardImageUrl() || "/placeholder.svg?height=380&width=240&query=physical+credit+card"}
                        alt={`${availableProducts.find((p) => p.version_code === formData.productType)?.version_name || "Card"} preview`}
                        className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="text-center mt-6 text-primary-foreground">
                    <h3 className="text-xl font-bold">
                      {availableProducts.find((p) => p.version_code === formData.productType)?.version_name ||
                          formData.productType}
                    </h3>
                    <p className="text-sm opacity-90">
                      {`${client?.personalInfo.firstName} ${client?.personalInfo.lastName}`}
                    </p>
                    <p className="text-sm opacity-90">{client?.personalInfo.email}</p>
                  </div>
                </div>
              </div>

                {/* Order Details Section - Right Side */}
                <div className="p-8 bg-card">
                <div className="space-y-6 max-w-xl mx-auto">
                  <div>
                    <h2 className="text-2xl font-bold text-foreground">Please Confirm Your Order</h2>
                    <p className="text-muted-foreground text-sm mt-1">
                      Review your card order details before proceeding
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div className="flex items-center gap-2 pb-3 border-b">
                      <CreditCard className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-lg">Card Details</h3>
                    </div>
                    <div className="grid gap-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Cardholder Name:</span>
                        <span className="font-medium">{`${client?.personalInfo.firstName} ${client?.personalInfo.lastName}`}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Product Type:</span>
                        <span className="font-medium">
                          {availableProducts.find((p) => p.version_code === formData.productType)?.version_name ||
                              formData.productType}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Nickname:</span>
                        <span className="font-medium">{formData.nickname || "Not specified"}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 pb-3 border-b pt-4">
                      <MapPin className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-lg">Delivery Address</h3>
                    </div>
                    <div className="bg-muted/30 p-4 rounded-lg text-sm border border-border shadow-sm">
                      {client?.address && selectedAddress === "default" ? (
                          <address className="not-italic">
                          {client.address.buildingNumber} {client.address.street}
                              {client.address.apartment ? `, ${client.address.apartment}` : ""}
                              <br />
                              {client.address.city}, {client.address.stateProvince} {client.address.zipCode}
                              <br />
                              {ListOfCountries.find((c) => c.numericCode === client.address.country)?.countryName || ""}
                        </address>
                      ) : (
                          <address className="not-italic">{formData.deliveryAddress}</address>
                      )}
                    </div>

                    <div className="flex items-center gap-2 pb-3 border-b pt-4">
                      <Truck className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-lg">Delivery Details</h3>
                    </div>
                    <div className="grid gap-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Delivery Method:</span>
                        <span className="font-medium flex items-center gap-2">
                          {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.methodId || "N/A"}
                            {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.source === "pp" && (
                                <Badge variant="outline" className="text-xs">
                              PP
                            </Badge>
                            )}
                            {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.methodId ===
                                "DHL Express" && (
                                    <Badge className="text-xs bg-orange-100 text-orange-800">
                              <Zap className="h-3 w-3 mr-1" />
                              Express
                            </Badge>
                                )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Zone/Region:</span>
                        <span className="font-medium">
                          {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.zoneName || "N/A"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Country:</span>
                        <span className="font-medium">
                          {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.country || "N/A"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Delivery Price:</span>
                        <span
                            className={`font-medium ${combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.methodId === "DHL Express" ? "text-orange-600" : "text-primary"}`}
                        >
                          {formatPrice(combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.price) ||
                              "N/A"}
                        </span>
                      </div>
                        {cardIssuingFee !== null && (
                            <div className="flex justify-between">
                          <span className="text-muted-foreground">Card Issuing Fee:</span>
                          <span className="font-medium text-primary">{formatPrice(cardIssuingFee)}</span>
                        </div>
                        )}
                        <div className="flex justify-between border-t pt-2 font-semibold">
                        <span className="text-foreground">Total Cost:</span>
                        <span className="text-primary">{formatPrice(getTotalOrderCost())}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Estimated Delivery:</span>
                        <span className="font-medium">
                          {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.deliveryTime || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-4 justify-end pt-6 border-t mt-6">
                    <Button variant="outline" onClick={() => setShowConfirmation(false)} className="px-6">
                      Cancel
                    </Button>
                    <Button onClick={handleConfirmOrder} disabled={isSubmitting} className="px-8 py-2 text-white">
                      {isSubmitting ? (
                          <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Processing...
                        </div>
                      ) : (
                          <div className="flex items-center gap-2">
                          <Check className="h-4 w-4" />
                          Order Card
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    )
}
