//@ts-nocheck
"use client"

import React, {useEffect, useState} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {Copy, CreditCard, List, Settings, Phone, Shield, Key, Snowflake, Loader2} from "lucide-react"
import {CardDetailsModal} from "./card-details-modal"
import {CardControlsModal} from "./card-controls-modal"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import Image from "next/image"
import {LockCardDialog} from "@/app/lite/admin/individual/v1/[id]/lock-card"
import {UnLockCardDialog} from "@/app/lite/admin/individual/v1/[id]/unlock-card"
import {ReplaceCardDialog} from "@/app/lite/admin/individual/v1/[id]/ReplaceCardDialog"
import {RestrictCardDialog} from "@/app/lite/admin/individual/v1/[id]/card/RestrictCardDialog"
import {ResignCardDialog} from "@/app/lite/admin/individual/v1/[id]/ResignCardDialog"
import {ForcePinLockDialog} from "@/app/lite/admin/individual/v1/[id]/ForcePinLockDialog"
import {SetCardLimitsDialog} from "@/app/lite/admin/individual/v1/[id]/card/SetCardLimitsDialog"
import {PinManagementDialog} from "@/app/lite/admin/individual/v1/[id]/card/pin-management-dialog"
import {ActivateCardDialog} from "@/app/lite/admin/individual/v1/[id]/ActivateCardDialog"
import {Tabs, TabsContent, TabsList, TabsTrigger} from "@/components/ui/tabs"
import {Table, TableBody, TableCell, TableRow} from "@/components/ui/table"
import axios from "@/utils/axiosInstance"
import type {CardImage} from "@types/types"
import {LoadingOverlay} from "@/components/LoadingOverlay"
import Swal from "sweetalert2"
import {useRouter} from "next/navigation"
import Link from "next/link"
import {Change3DSecurePhoneDialog} from "@/app/lite/admin/individual/v1/[id]/card/change-3d-auth-phone"
import PhoneNumberDisplay from "@/components/PhoneDispaly"
import {ChangeCardNicknameDialog} from "@/app/lite/admin/individual/v1/[id]/card/change-card-nickname-dialog"
import {SOTPDialog} from "@/app/lite/admin/individual/v1/[id]/card/otp-modal"
import {Badge} from "@/components/ui/badge"
import {Change3DSecureDialog} from "@/app/lite/admin/individual/v1/[id]/card/Change3DSecureDialog";
import {useAppSelector} from "@/store/hooks";

interface CardData {
    cardHash: string
    cardMask: string
    cardKey: string
    cardUuid: string
    expDate: string
    issueDate: string
    status: string
    statusCode: string
    terminating: boolean
    productCode: string
    productDesc: string
    holder: string
    accounts: Array<{
        accNo: string
        primary: boolean
        currencyCode: string
        currencyName: string
    }>
    embossName1: string
    source: string
    kind: string
    main: boolean
    limits: Array<{
        name: string
        value: number
        noLimit: boolean
    }>
    visual: string
    tokenized: boolean
    delivery: {
        deliveryType: string
        deliveryAddress: string
    }
    contactless: boolean
    cardTechnologyMode: string
    creationDate: string
    autoRenewal: boolean
}

interface CardDetailsModalProps {
    card: CardData
    dbCard: any
    companyId: string
    onApiSuccess: () => {}
}

const asset = process.env.NEXT_PUBLIC_ASSET_URL;

export function CardManagement({card, dbCard, onApiSuccess, companyId}: CardDetailsModalProps) {
    const [showCardDetails, setShowCardDetails] = useState(false)
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [showCardControls, setShowCardControls] = useState(false)
    const [currentImage, setCurrentImage] = useState(null)
    const [isFlipped, setIsFlipped] = useState(false)
    const [imageLoading, setImageLoading] = useState(true)
    const [imageError, setImageError] = useState(false)
    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)

    const checkPermission = (p) => {

        if (user.dashboard === "programmeManager") {
            // If dashboard is programmeManager, check permissions
            return user.pm.permissions.some(permission =>
                permission.toLowerCase().includes(p.toLowerCase())
            );
        }else if (user.dashboard === "corporate") {
            // If dashboard is programmeManager, check permissions
            return user.corporate.permissions.some(permission =>
                permission.toLowerCase().includes(p.toLowerCase())
            );
        }else if (user.dashboard === "cardholder") {
            // If dashboard is programmeManager, check permissions
            return user.cardholder.permissions.some(permission =>
                permission.toLowerCase().includes(p.toLowerCase())
            );
        } else {
            return true;
        }

    }

    // Function to show notification
    const showNotification = (message: string, alertType: "success" | "error" | "warning" | "info" = "success") => {
        Swal.fire({
            icon: alertType,
            title: alertType.charAt(0).toUpperCase() + alertType.slice(1),
            text: message,
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
        })
    }

    useEffect(() => {
        fetchData()

        // Cleanup function
        return () => {
            setCurrentImage(null)
            setImageLoading(false)
            setImageError(false)
        }
    }, [card.cardKey, companyId]) // Add dependencies to reload when card or company changes

    const fetchData = async () => {
        try {
            setIsLoading(true)
            setImageLoading(true)
            setImageError(false)
            setError(null)

            // Force fresh fetch by adding timestamp
            const timestamp = new Date().getTime()
            const response = await axios.get<CardImage[]>(`/images?t=${timestamp}`)
            setCardImages(response.data.data)

            const default_company = process.env.DEFAULT_COMPANY_ID;
            const images = response.data.data.filter((r) => r.product_version.version_code === card.productCode)
            let company_images = images.find((r) => r.company._id === companyId)

            console.log("Found images for product:", card.productCode, images)
            console.log("Company images:", company_images)

            if (company_images === undefined) {
                company_images = images.find((r) => r.company._id === default_company)
                console.log("Using default company images:", company_images)
            }

            setCurrentImage(company_images)

            // Preload images if available
            if (company_images) {
                await preloadImages(company_images)
            } else {
                console.warn("No card images found for product code:", card.productCode)
            }
        } catch (error) {
            console.error("Error fetching data", error)
            setImageError(true)
            showNotification("Failed to fetch card images. Please try again later.", "error")
        } finally {
            setIsLoading(false)
            setImageLoading(false)
        }
    }

    // Add preload function
    const preloadImages = async (imageData) => {
        const promises = []

        if (imageData.front_side) {
            promises.push(
                new Promise((resolve, reject) => {
                    const img = new Image()
                    img.crossOrigin = "anonymous"
                    img.onload = resolve
                    img.onerror = reject
                    img.src = `${asset}${imageData.front_side}`
                }),
            )
        }

        if (imageData.back_side) {
            promises.push(
                new Promise((resolve, reject) => {
                    const img = new Image()
                    img.crossOrigin = "anonymous"
                    img.onload = resolve
                    img.onerror = reject
                    img.src = `${asset}${imageData.back_side}`
                }),
            )
        }

        try {
            await Promise.all(promises)
        } catch (error) {
            console.error("Error preloading images:", error)
            setImageError(true)
        }
    }

    const splitCardMask = (cardMask) => {
        if (cardMask) {
            const cleanedMask = cardMask.replace(/\s+/g, "")
            const chunks = []
            for (let i = 0; i < cleanedMask.length; i += 4) {
                chunks.push(cleanedMask.slice(i, i + 4))
            }
            return chunks
        }
        return []
    }

    function getStatusColor(status: string) {
        switch (status.toUpperCase()) {
            case "ACTIVE":
                return "bg-green-500"
            case "INACTIVE":
                return "bg-yellow-500"
            case "BLOCKED":
                return "bg-red-500"
            case "ORDERED":
                return "bg-blue-500"
            default:
                return "bg-gray-500"
        }
    }

    function getStatusVariant(status: string) {
        switch (status.toUpperCase()) {
            case "ACTIVE":
                return "default"
            case "INACTIVE":
                return "secondary"
            case "BLOCKED":
                return "destructive"
            case "ORDERED":
                return "outline"
            default:
                return "secondary"
        }
    }

    if (isLoading) {
        return <LoadingOverlay/>
    }

    const cardMaskChunks = splitCardMask(card?.cardMask)
    const isVirtual = card.cardTechnologyMode.toUpperCase() === "VIRTUAL"
    const isDual = card.cardTechnologyMode.toUpperCase() === "DUAL"

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
            <div className="max-w-7xl mx-auto space-y-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Card Management</h1>
                        <p className="text-gray-600 mt-1">Manage your card settings and view details</p>
                    </div>
                    <Badge variant={getStatusVariant(card.status)} className="text-sm px-3 py-1">
                        {card.status}
                    </Badge>
                </div>

                {/* Main Content */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Card Display Section */}
                    <div className="lg:col-span-2">
                        <Card className="overflow-hidden shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                            <CardHeader className="pb-4">
                                <div className="flex items-center justify-between">
                                    <CardTitle
                                        className="text-xl">{isVirtual ? "Virtual Card" : "Physical Card"}</CardTitle>
                                    <div className="flex items-center gap-2">
                                        {isDual && (
                                            <Button variant="outline" size="sm" onClick={() => setIsFlipped(!isFlipped)}
                                                    className="text-xs">
                                                Flip Card
                                            </Button>
                                        )}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={fetchData}
                                            disabled={isLoading || imageLoading}
                                            className="text-xs bg-transparent"
                                        >
                                            {imageLoading ? (
                                                <div
                                                    className="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
                                            ) : (
                                                "Refresh"
                                            )}
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="pb-8">
                                <div className="flex justify-center mb-8">
                                    {isVirtual ? (
                                        <VirtualCard
                                            card={card}
                                            currentImage={currentImage}
                                            asset={asset}
                                            cardMaskChunks={cardMaskChunks}
                                            isFlipped={isFlipped}
                                            imageLoading={imageLoading}
                                            imageError={imageError}
                                        />
                                    ) : (
                                        <PhysicalCard
                                            card={card}
                                            currentImage={currentImage}
                                            asset={asset}
                                            cardMaskChunks={cardMaskChunks}
                                            isFlipped={isFlipped}
                                            imageLoading={imageLoading}
                                            imageError={imageError}
                                        />
                                    )}
                                </div>

                                {/* Quick Actions */}
                                <div className="flex justify-center gap-4">

                                    {checkPermission("View Details") && (
                                        <Button variant="ghost" onClick={() => setShowCardDetails(true)}
                                                className="flex flex-col items-center gap-2 h-auto py-3 hover:bg-green-50">
                                            <div
                                                className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                                                <CreditCard className="h-6 w-6 text-green-600"/>
                                            </div>
                                            <span className="text-sm">Card Details</span>
                                        </Button>

                                    )}
                                    {checkPermission("View Transactions") && (
                                        <Link href={`${card.cardKey}/transaction`}>
                                            <Button variant="ghost"
                                                    className="flex flex-col items-center gap-2 h-auto py-3 hover:bg-green-50">
                                                <div
                                                    className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                                                    <List className="h-6 w-6 text-green-600"/>
                                                </div>
                                                <span className="text-sm">Transactions</span>
                                            </Button>
                                        </Link>
                                    )}
                                    {checkPermission("Freeze Card") && (
                                        <>
                                            {card.status.toUpperCase() === "BLOCKED" ? (
                                                <UnLockCardDialog
                                                    onApiSuccess={() => onApiSuccess(card.cardKey)}
                                                    expDate={card.expDate}
                                                    cardId={card.cardKey}
                                                />
                                            ) : (
                                                card.status.toUpperCase() === "ACTIVE" && (
                                                    <LockCardDialog
                                                        onApiSuccess={() => onApiSuccess(card.cardKey)}
                                                        expDate={card.expDate}
                                                        cardId={card.cardKey}
                                                    />
                                                )
                                            )}
                                        </>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Card Info Sidebar */}
                    <div className="space-y-6">
                        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center gap-2">
                                    <Settings className="h-5 w-5"/>
                                    Card Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                        <span className="text-sm text-gray-600">Card Type</span>
                                        <span className="text-sm font-medium">{card.cardTechnologyMode}</span>
                                    </div>
                                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                        <span className="text-sm text-gray-600">Product</span>
                                        <span className="text-sm font-medium">{card.productDesc}</span>
                                    </div>
                                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                        <span className="text-sm text-gray-600">Issue Date</span>
                                        <span className="text-sm font-medium">{card.issueDate}</span>
                                    </div>
                                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                        <span className="text-sm text-gray-600">Expiry Date</span>
                                        <span className="text-sm font-medium">{card.expDate}</span>
                                    </div>
                                    <div className="flex justify-between items-center py-2">
                                        <span className="text-sm text-gray-600">Contactless</span>
                                        <Badge variant={card.contactless ? "default" : "secondary"} className="text-xs">
                                            {card.contactless ? "Enabled" : "Disabled"}
                                        </Badge>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Tabs Section */}
                <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <Tabs defaultValue="manage" className="w-full">
                            <TabsList className="grid w-full grid-cols-3 mb-6">
                                <TabsTrigger value="manage" className="flex items-center gap-2">
                                    <Settings className="h-4 w-4"/>
                                    Manage Card
                                </TabsTrigger>
                                <TabsTrigger value="3ds" className="flex items-center gap-2">
                                    <Shield className="h-4 w-4"/>
                                    3DS Settings
                                </TabsTrigger>
                                {checkPermission("otp") &&(
                                <TabsTrigger value="otp" className="flex items-center gap-2">
                                    <Key className="h-4 w-4"/>
                                    3DS OTP
                                </TabsTrigger>
                                    )}
                            </TabsList>

                            <TabsContent value="manage" className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {card.status.toUpperCase() === "ACTIVE" ? (
                                        <>
                                            {checkPermission("Change Nickname") && (
                                                <ChangeCardNicknameDialog
                                                    cardId={card.cardKey}
                                                    currentNickname={dbCard?.nickName}
                                                    onApiSuccess={() => onApiSuccess(card.cardKey)}
                                                />
                                            )}
                                            {checkPermission("Pin Management") && (
                                                <PinManagementDialog
                                                    cardId={card.cardKey}
                                                    expDate={card.expDate}
                                                    onApiSuccess={() => {
                                                        onApiSuccess(card.cardKey).then(() => {
                                                            showNotification("PIN Changed Successfully", "success")
                                                        })
                                                    }}
                                                />)}
                                            {checkPermission("Set Card Limits") && (
                                                <SetCardLimitsDialog
                                                    cardId={card.cardKey}
                                                    onApiSuccess={() => {
                                                        onApiSuccess(card.cardKey).then(() => {
                                                            showNotification("Limit Updated Successfully", "success")
                                                        })
                                                    }}
                                                />
                                            )}
                                            {checkPermission("Force Pin Lock") && (
                                                <ForcePinLockDialog
                                                    cardId={card.cardKey}
                                                    onApiSuccess={() => {
                                                        onApiSuccess(card.cardKey).then(() => {
                                                            showNotification("Pin Tries Reset Successfully", "success")
                                                        })
                                                    }}
                                                />
                                            )}
                                            {checkPermission("Resign Card") && (
                                                <ResignCardDialog
                                                    cardId={card.cardKey}
                                                    expDate={card.expDate}
                                                    onApiSuccess={() => {
                                                        onApiSuccess(card.cardKey).then(() => {
                                                            showNotification("Card resigned successfully", "success")
                                                        })
                                                    }}
                                                />)}
                                            {checkPermission("Report Card") && (
                                                <RestrictCardDialog
                                                    onApiSuccess={() => onApiSuccess(card.cardKey)}
                                                    expDate={card.expDate}
                                                    cardId={card.cardKey}
                                                />
                                            )}
                                            {checkPermission("Replace Card") && (
                                                <ReplaceCardDialog
                                                    onApiSuccess={() => onApiSuccess(card.cardKey)}
                                                    expDate={card.expDate}
                                                    cardId={card.cardKey}
                                                />
                                            )}
                                            {checkPermission("Change 3d Password") && (
                                                <Change3DSecureDialog
                                                    onApiSuccess={() => onApiSuccess(card.cardKey)}
                                                    expDate={card.expDate}
                                                    cardId={card.cardKey}
                                                />
                                            )}
                                        </>
                                    ) : (
                                        card.cardTechnologyMode.toUpperCase() === "DUAL" && (
                                            <ActivateCardDialog
                                                cardId={card.cardKey}
                                                expDate={card.expDate}
                                                onApiSuccess={() => {
                                                    onApiSuccess(card.cardKey).then(() => {
                                                        showNotification("Card Activated Successfully", "success")
                                                    })
                                                }}
                                            />
                                        )
                                    )}
                                </div>
                            </TabsContent>

                            <TabsContent value="3ds">
                                <Card className="border-0 shadow-sm">
                                    <CardContent className="p-6">
                                        <Table>
                                            <TableBody>
                                                <TableRow className="border-b">
                                                    <TableCell className="font-medium py-4">
                                                        <div className="flex items-center gap-2">
                                                            <Phone className="h-4 w-4 text-gray-500"/>
                                                            3DS Phone Number
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="py-4">
                                                        <div className="flex items-center gap-2">
                                                            <PhoneNumberDisplay
                                                                phoneNumber={dbCard?.onboarding?.personalInfo?.authPhoneNumber}/>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className="h-8 w-8 p-0"
                                                                onClick={() => {
                                                                    navigator.clipboard.writeText(dbCard?.onboarding?.personalInfo?.authPhoneNumber)
                                                                    showNotification("Phone number copied to clipboard", "success")
                                                                }}
                                                            >
                                                                <Copy
                                                                    className="h-4 w-4 text-gray-500 hover:text-primary"/>
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="py-4">
                                                        {checkPermission("Change 3d Phone") &&(
                                                        <Change3DSecurePhoneDialog
                                                            cardId={card.cardKey}
                                                            expDate={card.expDate}
                                                            onApiSuccess={() => {
                                                                onApiSuccess(card.cardKey).then(() => {
                                                                    showNotification("Auth phone number changed successfully", "success")
                                                                })
                                                            }}
                                                        />
                                                        )}
                                                    </TableCell>
                                                </TableRow>
                                            </TableBody>
                                        </Table>
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            <TabsContent value="otp">
                                <Card className="border-0 shadow-sm">
                                    <CardContent className="p-6">
                                        {checkPermission("OTP") && (
                                        <SOTPDialog
                                            cardId={card.cardKey}
                                            expDate={card.expDate}
                                            onApiSuccess={() => {
                                                onApiSuccess(card.cardKey).then(() => {
                                                    showNotification("3D Answer changed Successfully", "success")
                                                })
                                            }}
                                        />
                                        )}
                                    </CardContent>
                                </Card>
                            </TabsContent>
                        </Tabs>
                    </CardContent>
                </Card>
            </div>

            <CardDetailsModal open={showCardDetails} onOpenChange={setShowCardDetails} card={card}/>
            <CardControlsModal open={showCardControls} onOpenChange={setShowCardControls} card={card}/>
        </div>
    )
}

// Virtual Card Component
function VirtualCard({card, currentImage, asset, cardMaskChunks, isFlipped, imageLoading, imageError}) {
    return (
        <div className="relative">
            <div className={`card-3d-virtual ${isFlipped ? "flipped" : ""}`}>
                <div className="card-face card-front">
                    <div className="relative w-96 h-60 rounded-2xl overflow-hidden shadow-2xl bg-gray-100">
                        {imageLoading ? (
                            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                                <div className="flex flex-col items-center gap-2">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span className="text-sm text-gray-600">Loading card image...</span>
                                </div>
                            </div>
                        ) : (
                            <Image
                                src={
                                    currentImage?.front_side ? `${asset}${currentImage.front_side}?t=${Date.now()}` : "/cards/pp-fd.svg"
                                }
                                alt="Virtual Card Front"
                                fill
                                className="object-cover"
                                unoptimized
                                priority
                                onError={(e) => {
                                    e.currentTarget.src = "/cards/pp-fd.svg"
                                }}
                            />
                        )}
                        <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent">
                            <div className="absolute bottom-6 left-6 text-white">
                                <div className="text-xl font-mono tracking-wider mb-3">
                                    •••• •••• •••• {cardMaskChunks[3] || "••••"}
                                </div>
                                <div className="flex justify-between items-end w-80">
                                    <div>
                                        <div className="text-xs opacity-80">VALID THRU</div>
                                        <div className="text-sm font-medium">{card.expDate}</div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-xs opacity-80">CARDHOLDER</div>
                                        <div className="text-sm font-medium">{card.embossName1}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="card-face card-back">
                    <div className="relative w-96 h-60 rounded-2xl overflow-hidden shadow-2xl bg-gray-100">
                        {imageLoading ? (
                            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                                <div className="flex flex-col items-center gap-2">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span className="text-sm text-gray-600">Loading card image...</span>
                                </div>
                            </div>
                        ) : (
                            <Image
                                src={currentImage?.back_side ? `${asset}${currentImage.back_side}?t=${Date.now()}` : "/cards/pp-bd.svg"}
                                alt="Virtual Card Back"
                                fill
                                className="object-cover"
                                unoptimized
                                priority
                                onError={(e) => {
                                    e.currentTarget.src = "/cards/pp-bd.svg"
                                }}
                            />
                        )}
                        <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent">
                            <div className="absolute top-8 left-6 right-6">
                                <div className="bg-black h-8 w-full mb-4"></div>
                                <div className="text-white text-sm font-mono">{card.cardMask}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

// Physical Card Component
function PhysicalCard({card, currentImage, asset, cardMaskChunks, isFlipped, imageLoading, imageError}) {
    return (
        <div className="relative">
            <div className={`card-3d-physical ${isFlipped ? "flipped" : ""}`}>
                <div className="card-face card-front">
                    <div
                        className="relative rounded-2xl overflow-hidden shadow-2xl bg-gray-100"
                        style={{width: "330px", height: "510px"}}
                    >
                        {imageLoading ? (
                            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                                <div className="flex flex-col items-center gap-2">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span className="text-sm text-gray-600">Loading card image...</span>
                                </div>
                            </div>
                        ) : (
                            <Image
                                src={
                                    currentImage?.front_side
                                        ? `${asset}${currentImage.front_side}?t=${Date.now()}`
                                        : "/cards/physical-front.png"
                                }
                                alt="Physical Card Front"
                                width={330}
                                height={510}
                                className="object-cover"
                                unoptimized
                                priority
                                onError={(e) => {
                                    e.currentTarget.src = "/cards/physical-front.png"
                                }}
                            />
                        )}
                        {/*<div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent">*/}
                        {/*    <div className="absolute bottom-20 left-8 text-white">*/}
                        {/*        <div className="text-lg font-mono tracking-wider mb-8">*/}
                        {/*            •••• •••• •••• {cardMaskChunks[3] || "••••"}*/}
                        {/*        </div>*/}
                        {/*        <div className="space-y-6">*/}
                        {/*            <div>*/}
                        {/*                <div className="text-xs opacity-80">VALID THRU</div>*/}
                        {/*                <div className="text-base font-medium">{card.expDate}</div>*/}
                        {/*            </div>*/}
                        {/*            <div>*/}
                        {/*                <div className="text-xs opacity-80">CARDHOLDER</div>*/}
                        {/*                <div className="text-base font-medium">{card.embossName1}</div>*/}
                        {/*            </div>*/}
                        {/*        </div>*/}
                        {/*    </div>*/}
                        {/*</div>*/}
                    </div>
                </div>
                <div className="card-face card-back">
                    <div
                        className="relative rounded-2xl overflow-hidden shadow-2xl bg-gray-100"
                        style={{width: "330px", height: "510px"}}
                    >
                        {imageLoading ? (
                            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                                <div className="flex flex-col items-center gap-2">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span className="text-sm text-gray-600">Loading card image...</span>
                                </div>
                            </div>
                        ) : (
                            <Image
                                src={
                                    currentImage?.back_side ? `${asset}${currentImage.back_side}?t=${Date.now()}` : "/cards/back-vp.png"
                                }
                                alt="Physical Card Back"
                                width={330}
                                height={510}
                                className="object-cover"
                                unoptimized
                                priority
                                onError={(e) => {
                                    e.currentTarget.src = "/cards/back-vp.png"
                                }}
                            />
                        )}
                        <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent">
                            <div className="absolute top-20 left-8 right-8">
                                <div className="bg-transparent w-full mb-5"></div>
                                <div
                                    className="text-white space-y-3 mr-2 mb-12 text-right"> {/* Added text-right here */}
                                    {cardMaskChunks.map((chunk, index) => (
                                        <div key={index} className="text-base font-mono">
                                            {chunk}
                                        </div>
                                    ))}

                                    <div className="text-base pt-2  font-mono">
                                        {card.expDate}
                                    </div>

                                </div>
                                <br/>
                                <div className=" mt-5  bottom-12  text-right w-full  left-8 text-white">
                                    <div className="text-base font-bold">{card.embossName1}</div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    )
}


