//@ts-nocheck

"use client"

import type React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
    Loader2,
    CheckCircle2,
    CreditCard,
    Building2,
    Settings,
    ImageIcon,
    ArrowRight,
    ArrowLeft,
    Shield,
    Zap,
} from "lucide-react"
import { useEffect, useState, useCallback } from "react"
import { useRout<PERSON> } from "next/navigation"
import axiosInstance from "@/utils/axiosInstance"
import { CustomCheckbox } from "@/components/CustomCheckbox"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { alertHelper } from "@/utils/alertHelper"

const asset = process.env.NEXT_PUBLIC_API_URL

// Interfaces
interface FormData {
    binRangeId: string
    cardScheme: string
    programmeType: string
    binType: string
    company: string
    programManagerType: string
    productVersionName: string[]
    created_by: string | null
    binRange: string
    bin_start: string
    bin_end: string
    bin_prefix: string
    bin_suffix: string
}

interface DropdownData {
    cardSchemes: any[]
    binTypes: any[]
    programmeTypes: any[]
    managerTypes: any[]
    binRanges: any[]
    productVersions: any[]
}

interface FilteredOptions {
    filteredBinTypes: any[]
    filteredManagerTypes: any[]
    filteredProductVersions: any[]
}

const INITIAL_FORM_DATA = {
    binRangeId: "",
    cardScheme: "",
    programmeType: "",
    binType: "",
    company: "",
    programManagerType: "",
    productVersionName: [],
    created_by: null,
    binRange: "",
    bin_start: "",
    bin_end: "",
    bin_prefix: "",
    bin_suffix: "",
}

// Progress Steps Component
const ProgressSteps = ({ currentStep, totalSteps }: { currentStep: number; totalSteps: number }) => {
    const progress = (currentStep / totalSteps) * 100
    return (
        <div className="mb-8">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-muted-foreground">
          Step {currentStep} of {totalSteps}
        </span>
        <span className="text-sm font-medium text-primary">{Math.round(progress)}% Complete</span>
      </div>
      <Progress value={progress} className="h-2" />
    </div>
    )
}

export default function CardIssuingForm({ params }: { params: { id: string } }) {
    const router = useRouter()

    // Form state
    const [formData, setFormData] = useState<FormData>({
        ...INITIAL_FORM_DATA,
        company: params.id,
        created_by: localStorage.getItem("user"),
    })

    // Dropdown data state
    const [dropdownData, setDropdownData] = useState<DropdownData>({
        cardSchemes: [],
        binTypes: [],
        programmeTypes: [],
        managerTypes: [],
        binRanges: [],
        productVersions: [],
    })

    // Company and related data
    const [companyData, setCompanyData] = useState<any>(null)
    const [cardImages, setCardImages] = useState([])
    const [existingCipVersions, setExistingCipVersions] = useState<string[]>([])

    // UI state
    const [loading, setLoading] = useState(true)
    const [dropdownLoading, setDropdownLoading] = useState(true)
    const [showSuccessDialog, setShowSuccessDialog] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)

    // Memoized filtered options
    const [filteredBinTypes, setFilteredBinTypes] = useState<any[]>([])
    const [filteredManagerTypes, setFilteredManagerTypes] = useState<any[]>([])
    const [filteredProductVersions, setFilteredProductVersions] = useState<any[]>([])

    // Calculate current step for progress
    const currentStep = formData.cardScheme
        ? 1
        : 0 +
        (formData.programmeType ? 1 : 0) +
        (formData.binType ? 1 : 0) +
        (formData.programManagerType ? 1 : 0) +
        (formData.productVersionName.length > 0 ? 1 : 0)

    // Form validation
    const isFormValid = formData.cardScheme && formData.binType && formData.productVersionName.length > 0

    // Enhanced form change handler
    const handleFormChange = useCallback(
        (field: keyof FormData, value: any) => {
            setFormData((prev) => {
                let newState = { ...prev }

                if (field === "productVersionName") {
                    // Check if version already exists
                    const version = dropdownData.productVersions.find((v) => v._id === value)
                    if (version && existingCipVersions.includes(version.version_code)) {
                        return prev
                    }

                    newState.productVersionName = prev.productVersionName.includes(value)
                        ? prev.productVersionName.filter((v) => v !== value)
                        : [...prev.productVersionName, value]
                } else {
                    newState[field] = value

                    // Reset dependent fields when parent fields change
                    if (field === "cardScheme") {
                        newState = {
                            ...newState,
                            binType: "",
                            programManagerType: "",
                            productVersionName: [],
                        }
                    } else if (field === "programmeType") {
                        newState = {
                            ...newState,
                            binType: "",
                            programManagerType: "",
                            productVersionName: [],
                        }
                    }
                }

                return newState
            })

            // Handle side effects for bin type selection
            if (field === "binType" && value) {
                const selectedBinType = dropdownData.binTypes.find((bt: any) => bt._id === value)
                const binRange = dropdownData.binRanges.find((br: any) => br.binType._id === value)

                if (selectedBinType && binRange) {
                    // Update BIN range related fields
                    setFormData((prev) => ({
                        ...prev,
                        binRangeId: binRange._id,
                        binRange: binRange.binCode,
                        bin_suffix: binRange.binCodeSuffix,
                        bin_prefix: binRange.binCodePrefix,
                        bin_start: binRange.bin_start,
                        bin_end: binRange.bin_end,
                    }))
                }
            }
        },
        [dropdownData, existingCipVersions],
    )

    // Auto-select manager type if only one option
    useEffect(() => {
        if (dropdownData.managerTypes.length === 1 && !formData.programManagerType) {
            handleFormChange("programManagerType", dropdownData.managerTypes[0]._id)
        }
    }, [dropdownData.managerTypes, formData.programManagerType, handleFormChange])

    // Fetch dropdown data
    useEffect(() => {
        const fetchDropdownData = async () => {
            try {
                setDropdownLoading(true)
                const [schemes, versions, manager, pts, bts, brs] = await Promise.all([
                    axiosInstance.get("cardScheme/schemes"),
                    axiosInstance.get("product-versions"),
                    axiosInstance.get("/programme-manager-types"),
                    axiosInstance.get("/programme-type"),
                    axiosInstance.get("/bin-types"),
                    axiosInstance.get("/bin-range"),
                ])

                setDropdownData({
                    cardSchemes: schemes.data.data,
                    productVersions: versions.data,
                    managerTypes: manager.data,
                    programmeTypes: pts.data,
                    binTypes: bts.data.data,
                    binRanges: brs.data.data,
                })
            } catch (error) {
                console.error("Failed to fetch dropdown data:", error)
                alertHelper.showToast("Failed to fetch dropdown data", "error")
            } finally {
                setDropdownLoading(false)
            }
        }

        fetchDropdownData()
    }, [])

    // Fetch company data
    useEffect(() => {
        const fetchCompanyDetails = async () => {
            try {
                const [companyResponse, imagesResponse] = await Promise.all([
                    axiosInstance.get(`/company/${params.id}`),
                    axiosInstance.get("/images"),
                ])

                setCompanyData(companyResponse.data.company)

                // Filter images for this company
                const companyImages = imagesResponse.data.data.filter(
                    (image: any) => image.company._id === process.env.DEFAULT_COMPANY_ID,
                )
                setCardImages(companyImages)

                // Extract existing CIP versions
                const existingVersions = companyResponse.data.cip.flatMap((cip: any) =>
                    cip.productVersionName.map((product: any) => product.version_code),
                )
                setExistingCipVersions(existingVersions)
            } catch (error: any) {
                console.error("Failed to fetch company data:", error)
                alertHelper.showToast("Failed to fetch company data", "error")
            } finally {
                setLoading(false)
            }
        }

        fetchCompanyDetails()
    }, [params.id])

    // Submit handler
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)

        try {
            const response = await axiosInstance.post("cip/save-data", formData)
            if (response.status === 201) {
                setShowSuccessDialog(true)
            }
        } catch (error) {
            console.error("Failed to save data:", error)
            alertHelper.showToast("Failed to save data. Please try again.", "error")
        } finally {
            setIsSubmitting(false)
        }
    }

    useEffect(() => {
        const updateFilteredOptions = () => {
            // Filter bin types based on card scheme and programme type
            let filtered = dropdownData.binTypes.filter((binType: any) =>
                binType.type
                    .toLowerCase()
                    .startsWith(
                        dropdownData.cardSchemes
                            .find((scheme: any) => scheme._id === formData.cardScheme)
                            ?.scheme_name.toLowerCase() || "",
                    ),
            )

            // Further filter by programme type if selected
            if (formData.programmeType) {
                const selectedProgrammeType = dropdownData.programmeTypes.find(
                    (type: any) => type._id === formData.programmeType,
                )
                if (selectedProgrammeType) {
                    filtered = filtered.filter((binType: any) =>
                        binType.type.toLowerCase().includes(selectedProgrammeType.type.toLowerCase()),
                    )
                }
            }

            setFilteredBinTypes(filtered)

            // Filter manager types based on bin type
            const filteredManagers = dropdownData.managerTypes.filter(
                (manager: any) => manager.bin_type._id === formData.binType,
            )
            setFilteredManagerTypes(filteredManagers)

            // Filter product versions based on bin type and programme type
            const selectedBinType = dropdownData.binTypes.find((bt: any) => bt._id === formData.binType)
            const binRange = dropdownData.binRanges.find((br: any) => br.binType._id === formData.binType)

            if (selectedBinType && binRange) {


                setFilteredProductVersions(dropdownData.productVersions.filter((version: any) => version.status.toLowerCase() === "active" &&
                    version.version_name.toLowerCase().includes(companyData.company_name.toLowerCase())))

                console.log(filteredProductVersions)
            }
        }

        updateFilteredOptions()
    }, [formData.cardScheme, formData.programmeType, formData.binType, dropdownData, companyData])

    if (loading || dropdownLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4">
        <div className="container mx-auto max-w-4xl">
          <LoadingOverlay />
        </div>
      </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-100 p-4">
      <div className="container mx-auto max-w-4xl">
        {/* Header Section */}
          <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                <Building2 className="w-6 h-6 text-primary" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                  Card Issuing Programme
                </h1>
                <p className="text-muted-foreground">
                  Create Card Issuing Programme for{" "}
                    <span className="font-semibold text-primary">{companyData?.company_name}</span>
                </p>
              </div>
            </div>
            <div className="ml-auto">
              <Badge variant="secondary" className="gap-2">
                <Shield className="w-3 h-3" />
                Secure Setup
              </Badge>
            </div>
          </div>
          <ProgressSteps currentStep={currentStep} totalSteps={5} />
        </div>

          {/* Main Form */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardContent className="p-8">
            <form className="space-y-8" onSubmit={handleSubmit}>
              {/* CIP Details Section */}
                <Card className="border-2 border-dashed border-primary/20 bg-gradient-to-r from-primary/5 to-blue-50/50">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Settings className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">CIP Details</CardTitle>
                      <CardDescription>Configure your card issuing programme settings</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Card Scheme */}
                      <div className="space-y-3">
                      <Label htmlFor="cardScheme" className="text-sm font-semibold flex items-center gap-2">
                        <CreditCard className="w-4 h-4" />
                        Card Scheme
                      </Label>
                      <Select onValueChange={(value) => handleFormChange("cardScheme", value)}>
                        <SelectTrigger
                            id="cardScheme"
                            className="h-12 border-2 hover:border-primary/50 transition-colors"
                        >
                          <SelectValue placeholder="Select Card Scheme" />
                        </SelectTrigger>
                        <SelectContent>
                          {dropdownData.cardSchemes
                              .filter((t) => t.status.toLowerCase() === "active")
                              .map((scheme) => (
                                  <SelectItem key={scheme._id} value={scheme._id} className="py-3">
                                <div className="font-medium">{scheme.scheme_name}</div>
                              </SelectItem>
                              ))}
                        </SelectContent>
                      </Select>
                          {formData.cardScheme && (
                              <div className="flex items-center gap-2 text-sm text-green-600 animate-in slide-in-from-left-2 duration-300">
                          <CheckCircle2 className="w-4 h-4" />
                          Card scheme selected
                        </div>
                          )}
                    </div>

                      {/* Programme Type */}
                      <div className="space-y-3">
                      <Label htmlFor="programmeType" className="text-sm font-semibold">
                        Programme Type
                      </Label>
                      <Select
                          onValueChange={(value) => handleFormChange("programmeType", value)}
                          disabled={!formData.cardScheme}
                      >
                        <SelectTrigger
                            id="programmeType"
                            className="h-12 border-2 hover:border-primary/50 transition-colors"
                        >
                          <SelectValue
                              placeholder={
                                  formData.cardScheme ? "Select Programme Type" : "Please select a Card Scheme first"
                              }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {dropdownData.programmeTypes
                              .filter((t) => t.status.toLowerCase() === "active")
                              .map((type) => (
                                  <SelectItem key={type._id} value={type._id} className="py-3">
                                <div className="font-medium">{type.type}</div>
                              </SelectItem>
                              ))}
                        </SelectContent>
                      </Select>
                    </div>

                      {/* Bin Type */}
                      <div className="space-y-3">
                      <Label htmlFor="binType" className="text-sm font-semibold">
                        Bin Type
                      </Label>
                      <Select
                          disabled={!formData.programmeType}
                          onValueChange={(value) => handleFormChange("binType", value)}
                      >
                        <SelectTrigger id="binType" className="h-12 border-2 hover:border-primary/50 transition-colors">
                          <SelectValue
                              placeholder={
                                  formData.programmeType ? "Select Bin Type" : "Please select a Programme Type first"
                              }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {filteredBinTypes.map((type) => (
                              <SelectItem key={type._id} value={type._id} className="py-3">
                              <div className="font-medium">{type.type}</div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                      {/* Programme Manager Type */}
                      <div className="space-y-3">
                      <Label htmlFor="programManagerType" className="text-sm font-semibold">
                        Programme Manager Type
                      </Label>
                      <Select
                          value={formData.programManagerType}
                          disabled={!formData.binType}
                          onValueChange={(value) => handleFormChange("programManagerType", value)}
                      >
                        <SelectTrigger
                            id="programManagerType"
                            className="h-12 border-2 hover:border-primary/50 transition-colors"
                        >
                          <SelectValue
                              placeholder={
                                  formData.binType ? "Select Programme Manager Type" : "Please select a BIN Type first"
                              }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {filteredManagerTypes.map((type) => (
                              <SelectItem key={type._id} value={type._id} className="py-3">
                              <div className="font-medium">{type.manager_type}</div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                    {/* BIN Range Information */}
                    {formData.binType && (
                        <div className="mt-8 p-6 bg-white/60 rounded-xl border animate-in slide-in-from-bottom-4 duration-500">
                      <h4 className="font-semibold mb-4 flex items-center gap-2">
                        <Zap className="w-4 h-4 text-primary" />
                        Generated BIN Range Information
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                        {[
                            { label: "BIN Prefix", value: formData.bin_prefix },
                            { label: "BIN Suffix", value: formData.bin_suffix },
                            { label: "BIN Range", value: formData.binRange },
                            { label: "Range Start", value: formData.bin_start },
                            { label: "Range End", value: formData.bin_end },
                        ].map((item, index) => (
                            <div key={index} className="space-y-2">
                            <Label className="text-xs font-medium text-muted-foreground">{item.label}</Label>
                            <div className="p-3 bg-muted/50 rounded-lg border">
                              <code className="text-sm font-mono">{item.value || "---"}</code>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    )}
                </CardContent>
              </Card>

                {/* Product Versions Section */}
                {filteredProductVersions.length > 0 && (
                    <Card className="border-2 border-dashed border-green-200 bg-gradient-to-r from-green-50/50 to-emerald-50/30 animate-in slide-in-from-bottom-6 duration-700">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <ImageIcon className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">Product Versions</CardTitle>
                        <CardDescription>Select the card products you want to offer</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {filteredProductVersions.map((version, index) => {
                          const isVersionExisting = existingCipVersions.includes(version.version_code)
                          const shouldShowAsSelected =
                              isVersionExisting &&
                              filteredProductVersions.some((v) => v.version_code === version.version_code)

                          const cardImage = cardImages.find((img: any) => img.product_version._id === version._id)
                          const imageUrl = cardImage?.front_side ? `${asset}${cardImage.front_side}` : undefined

                          return (
                              <div
                                  className="animate-in fade-in-50 duration-300"
                                  style={{ animationDelay: `${index * 100}ms` }}
                                  key={version._id}
                              >
                            <CustomCheckbox
                                cardType={version.version_name.toLowerCase().includes("vtl") ? "vtl" : "phy"}
                                id={`version-${version._id}`}
                                label={`${version.version_name}${shouldShowAsSelected ? " (Already Selected)" : ""}`}
                                checked={formData.productVersionName.includes(version._id) || shouldShowAsSelected}
                                onChange={() => {
                                    if (!shouldShowAsSelected) {
                                        handleFormChange("productVersionName", version._id)
                                    }
                                }}
                                disabled={shouldShowAsSelected}
                                imageUrl={imageUrl}
                                imageAlt={`${version.version_code} card design`}
                            />
                          </div>
                          )
                      })}
                    </div>
                  </CardContent>
                </Card>
                )}

                {/* Submit Section */}
                <div className="flex items-center justify-between pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => router.back()} className="gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  Go Back
                </Button>
                <Button
                    type="submit"
                    disabled={!isFormValid || isSubmitting}
                    className="gap-2 px-8 h-12 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {isSubmitting ? (
                      <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                      <>
                      Submit
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

          {/* Success Dialog */}
          <AlertDialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
          <AlertDialogContent className="max-w-md">
            <AlertDialogHeader className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle2 className="w-8 h-8 text-green-600" />
              </div>
              <AlertDialogTitle className="text-2xl">Success!</AlertDialogTitle>
              <AlertDialogDescription className="text-base">
                Card Issuing Program created successfully!
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction onClick={() => router.back()} className="w-full h-12 text-base font-semibold">
                Go Back
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
    )
}
