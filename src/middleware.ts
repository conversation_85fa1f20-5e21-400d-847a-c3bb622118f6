import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// Enhanced JWT decoding function
const decodeJWT = (token: string) => {
  try {
    const parts = token.split(".")
    if (parts.length !== 3) {
      return null
    }

    const header = JSON.parse(atob(parts[0]))
    const payload = JSON.parse(atob(parts[1]))

    return {
      header,
      payload,
      raw: token,
      isExpired: payload.exp ? Date.now() >= payload.exp * 1000 : false,
      expiresAt: payload.exp ? new Date(payload.exp * 1000).toISOString() : null,
      issuedAt: payload.iat ? new Date(payload.iat * 1000).toISOString() : null,
    }
  } catch (error) {
    console.error("JWT decode error:", error)
    return null
  }
}

// Function to determine if we should log this activity - ONLY PAGE VISITS
function shouldLogActivity(request: NextRequest): boolean {
  const { pathname, searchParams } = request.nextUrl
  const userAgent = request.headers.get("user-agent") || ""
  const referer = request.headers.get("referer") || ""

  // Skip if it's a bot
  const isBot = (ua: string) => /bot|crawler|spider|scraper|facebookexternalhit|twitterbot|linkedinbot/i.test(ua)
  if (isBot(userAgent)) {
    return false
  }

  // Skip API routes, static files, and Next.js internals
  if (
    pathname.startsWith("/api/") ||
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/favicon") ||
    pathname.includes(".") ||
    pathname === "/robots.txt" ||
    pathname === "/sitemap.xml"
  ) {
    return false
  }

  if (referer) {
    try {
      const refererUrl = new URL(referer)
      const currentUrl = request.nextUrl

      // If referer is from the same domain and same pathname, it's likely a reload
      if (refererUrl.hostname === currentUrl.hostname && refererUrl.pathname === currentUrl.pathname) {
        return false
      }
    } catch (error) {
      // If referer URL is invalid, continue with logging
    }
  }

  const lastVisitKey = `last-visit-${pathname}`
  const lastVisitTime = request.cookies.get(lastVisitKey)?.value

  if (lastVisitTime) {
    const timeDiff = Date.now() - Number.parseInt(lastVisitTime)
    // Skip if same page was visited within last 5 seconds (likely a reload)
    if (timeDiff < 5000) {
      return false
    }
  }

  return true
}

// Function to extract user information
const extractUserInfo = (request: NextRequest) => {
  const authHeader = request.headers.get("authorization")
  const bearerToken = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : null

  const cookieToken =
    request.cookies.get("authToken")?.value

  const jwtToken = bearerToken || cookieToken
  const decodedJWT = jwtToken ? decodeJWT(jwtToken) : null
  const jwtPayload = decodedJWT?.payload

  const userId = jwtPayload?.sub || jwtPayload?.userId || jwtPayload?.id || jwtPayload?.user_id

  const userEmail = jwtPayload?.email || jwtPayload?.user_email

  const userName = jwtPayload?.name || jwtPayload?.username || jwtPayload?.user_name || jwtPayload?.displayName

  return {
    hasJWT: !!jwtToken,
    hasBearerToken: !!bearerToken,
    hasSessionToken: false,
    jwt: decodedJWT
      ? {
          header: decodedJWT.header,
          payload: decodedJWT.payload,
          raw: decodedJWT.raw,
          isExpired: decodedJWT.isExpired,
          expiresAt: decodedJWT.expiresAt,
          issuedAt: decodedJWT.issuedAt,
          algorithm: decodedJWT.header?.alg,
          tokenType: decodedJWT.header?.typ,
        }
      : null,
    userId,
    userEmail,
    userName,
    userProperties: jwtPayload
      ? {
          customClaims: Object.keys(jwtPayload)
            .filter((key) => !["sub", "iat", "exp", "iss", "aud", "email", "name", "role"].includes(key))
            .reduce((obj, key) => ({ ...obj, [key]: jwtPayload[key] }), {}),
        }
      : null,
    tokenSource: bearerToken ? "authorization_header" : cookieToken ? "cookie" : null,
  }
}

export async function middleware(request: NextRequest) {
  const response = NextResponse.next()
  const { pathname } = request.nextUrl

  if (shouldLogActivity(request)) {
    // Set cookie to track this visit
    const lastVisitKey = `last-visit-${pathname}`
    response.cookies.set(lastVisitKey, Date.now().toString(), {
      maxAge: 10, // 10 seconds
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    })

    // Get user info
    const userInfo = extractUserInfo(request)

    const activityData = {
      // Request information
      url: request.url,
      pathname: request.nextUrl.pathname,
      method: request.method,
      timestamp: new Date().toISOString(),
      searchParams: Object.fromEntries(request.nextUrl.searchParams),

      // Client information
      userAgent: request.headers.get("user-agent") || "",
      referer: request.headers.get("referer") || "",
      ip:
        request.ip ||
        request.headers.get("x-forwarded-for") ||
        request.headers.get("x-real-ip") ||
        request.headers.get("cf-connecting-ip") ||
        request.headers.get("x-vercel-forwarded-for") ||
        "unknown",

      // Browser/Client details
      headers: {
        accept: request.headers.get("accept") || "",
        "accept-language": request.headers.get("accept-language") || "",
        "sec-fetch-dest": request.headers.get("sec-fetch-dest") || "",
        "sec-fetch-mode": request.headers.get("sec-fetch-mode") || "",
        "sec-fetch-site": request.headers.get("sec-fetch-site") || "",
        "sec-ch-ua-platform": request.headers.get("sec-ch-ua-platform") || "",
      },

      // User information
      user: {
        ...userInfo,
        isAuthenticated: !!(userInfo.userId && userInfo.hasJWT),
        authenticationMethod: userInfo.hasJWT ? "jwt" : "none",
        sessionFingerprint: null,
      },

      // Geographic information
      geo: {
        country: request.headers.get("cf-ipcountry") || request.headers.get("x-vercel-ip-country") || null,
        region: request.headers.get("cf-region") || request.headers.get("x-vercel-ip-country-region") || null,
        city: request.headers.get("cf-ipcity") || request.headers.get("x-vercel-ip-city") || null,
        timezone: request.headers.get("cf-timezone") || request.headers.get("x-vercel-ip-timezone") || null,
      },

      // Device information
      device: {
        isMobile: /Mobile|Android|iPhone|iPad/.test(request.headers.get("user-agent") || ""),
        isTablet: /iPad|Android(?!.*Mobile)/.test(request.headers.get("user-agent") || ""),
        isDesktop: !/Mobile|Android|iPhone|iPad/.test(request.headers.get("user-agent") || ""),
        isBot: false, // We already filtered out bots
        platform: request.headers.get("sec-ch-ua-platform")?.replace(/"/g, "") || null,
        isMobileUA: request.headers.get("sec-ch-ua-mobile") === "?1",
      },

      // Request context
      context: {
        isApiRequest: false, // We only log page visits now
        isStaticAsset: false, // We filtered these out
        hasQueryParams: request.nextUrl.searchParams.size > 0,
        isPageNavigation: true, // All logged requests are page navigations
        isPageVisit: true, // New flag to clearly identify page visits
      },
    }

    // Send activity data to API endpoint (non-blocking)
    try {
      fetch(`${request.nextUrl.origin}/api/activity`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(activityData),
      }).catch(() => {
        // Silently handle errors to avoid blocking requests
      })
    } catch (error) {
      console.error("Middleware activity logging error:", error)
    }
  }

  return response
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except static files
     */
    "/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)",
  ],
}
